#!/usr/bin/env python3
"""
启动费用分析API服务
独立的费用分析模块启动脚本
"""

import sys
import os

# 添加项目根路径到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def main():
    """启动API服务"""
    print("💰 启动费用分析API服务")
    print("=" * 40)
    print("📍 端口: 8002")
    print("📖 文档: http://localhost:8002/docs")
    print("=" * 40)
    
    try:
        import uvicorn

        print("🚀 正在启动服务...")
        uvicorn.run(
            "src.expense_analysis.api.export_api:app",
            host="0.0.0.0",
            port=8002,
            reload=True,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install fastapi uvicorn pandas openpyxl")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
