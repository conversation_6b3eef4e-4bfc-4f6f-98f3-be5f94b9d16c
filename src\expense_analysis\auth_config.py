"""
费用分析模块鉴权配置
"""
import os
import base64
from typing import Set, Dict, Tuple, Optional

# API 鉴权配置
class AuthConfig:
    """鉴权配置"""
    
    # Token与数据库的映射关系 (Token -> 数据库名称)
    TOKEN_DATABASE_MAPPING: Dict[str, str] = {
        "8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1": "analysis_gz",     # 广州数据库
        "9a5d6e3f8c2b7a4e1d9f6c3b8e5a2d7f4c1b9e6": "analysis_bj",     # 北京数据库
        "7c2f5b8e1a4d9c6f3b8e5a2d7f4c1b9e6a3d8f5": "analysis_sh",     # 上海数据库
        "6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4": "analysis_jt",     # 集团数据库
        "5a0d3c6f9b2e7a4d1c8f5b2e9a6d3c0f7b4e1a9": "analysis_admin",  # 管理员数据库
        # 可以添加更多token-数据库映射
    }

    # 兼容旧版本的简单token列表（从映射中提取）
    @classmethod
    def get_valid_tokens(cls) -> Set[str]:
        """获取所有有效token"""
        tokens = set(cls.TOKEN_DATABASE_MAPPING.keys())

        # 从环境变量添加token（格式：token:database）
        env_token_db = os.getenv("EXPENSE_API_TOKEN_DB")
        if env_token_db and ":" in env_token_db:
            token, database = env_token_db.split(":", 1)
            cls.TOKEN_DATABASE_MAPPING[token] = database
            tokens.add(token)

        return tokens
    
    @classmethod
    def get_database_by_token(cls, token: str) -> Optional[str]:
        """根据token获取对应的数据库名称"""
        # 先检查映射表
        database = cls.TOKEN_DATABASE_MAPPING.get(token)
        if database:
            return database

        # 检查环境变量
        env_token_db = os.getenv("EXPENSE_API_TOKEN_DB")
        if env_token_db and ":" in env_token_db:
            env_token, env_database = env_token_db.split(":", 1)
            if env_token == token:
                return env_database

        return None

    @classmethod
    def is_valid_token(cls, token: str) -> bool:
        """检查token是否有效"""
        return token in cls.get_valid_tokens()
    
    # 是否启用鉴权 (可以通过环境变量关闭，方便开发调试)
    ENABLE_AUTH: bool = os.getenv("EXPENSE_API_AUTH_DISABLED", "false").lower() != "true"


# 使用示例:
# 1. 使用预设token访问对应数据库:
#    - Authorization: Bearer 8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1  (访问analysis_gz)
#    - Authorization: Bearer 9a5d6e3f8c2b7a4e1d9f6c3b8e5a2d7f4c1b9e6  (访问analysis_bj)
# 2. 设置环境变量: export EXPENSE_API_TOKEN_DB=your_token:your_database
# 3. 开发时关闭鉴权: export EXPENSE_API_AUTH_DISABLED=true
# 4. Token自动决定访问哪个数据库，用户无法直接修改
