# 🚀 向量增强系统快速启动指南

## 📋 功能概述

您的需求已经完全实现！系统现在支持：

1. **智能问题匹配** - 从向量数据库检索复杂提示词
2. **省份感知查询** - 自动提取省份代码，选择对应数据库
3. **Web管理界面** - 可视化管理复杂提示词
4. **完整API接口** - 支持CRUD操作和搜索功能

## 🎯 核心工作流程

```
用户问题: "当前电费情况如何"
    ↓
1. 检索向量数据库 (ChromaDB)
    ↓
2. 匹配到"电费情况综合分析"提示词
    ↓
3. 执行复杂处理步骤:
   - 查询最近3个月电费数据
   - 分析电费构成和趋势
   - 对比历史数据
   - 提供优化建议
    ↓
4. 按预定格式返回结构化报告
```

## ⚡ 快速启动

### 1. 配置Embedding服务

在`.env`文件中配置您的远程embedding服务（3个必需参数）：

```bash
# 启用远程embedding
REMOTE_EMBEDDING_ENABLED=true
# 您的embedding服务地址
REMOTE_EMBEDDING_URL=http://your-embedding-service:8001/embeddings
# API密钥
REMOTE_EMBEDDING_API_KEY=your-api-key
# 模型名称
REMOTE_EMBEDDING_MODEL=text-embedding-ada-002
```

**配置示例：**

OpenAI API:
```bash
REMOTE_EMBEDDING_URL=https://api.openai.com/v1/embeddings
REMOTE_EMBEDDING_API_KEY=sk-your-openai-key
REMOTE_EMBEDDING_MODEL=text-embedding-ada-002
```

自定义服务:
```bash
REMOTE_EMBEDDING_URL=http://your-server:8001/embeddings
REMOTE_EMBEDDING_API_KEY=your-custom-key
REMOTE_EMBEDDING_MODEL=your-model-name
```

### 2. 启动系统

```bash
# 启动API服务器
python examples/analysis_agent_server.py
```

### 3. 访问管理界面

打开浏览器访问：**http://localhost:8001/admin**

### 4. 测试Embedding配置

```bash
# 测试embedding配置是否正确
python test_embedding_config.py
```

### 5. 创建示例QA数据（可选）

```bash
# 创建示例复杂提示词
python examples/sample_complex_prompts.py
```

### 6. 测试QA数据功能

```bash
# 运行基础测试（不依赖embedding）
python test_system_basic.py
```

## 🎨 管理界面功能

访问 `http://localhost:8001/admin` 后，您可以：

### 📝 创建复杂提示词

1. 点击"创建提示词"标签页
2. 填写以下信息：
   - **标题**: 如"电费情况综合分析"
   - **描述**: 功能说明
   - **触发关键词**: 如"电费情况,电费分析,电费统计"
   - **处理步骤**: 每行一个步骤
   - **回复格式**: 期望的输出格式
   - **优先级**: 1-4，数字越大优先级越高

### 🔍 搜索测试

1. 点击"搜索测试"标签页
2. 输入用户可能的问题
3. 查看匹配结果和相似度分数
4. 验证匹配效果

### 📊 查看统计

1. 点击"统计信息"标签页
2. 查看提示词数量和分布
3. 监控系统使用情况

## 🧪 测试示例

### API调用示例

```bash
curl -X POST http://localhost:8001/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "analysis_agent",
    "messages": [
      {"role": "system", "content": "数据库:GZ"},
      {"role": "user", "content": "当前电费情况如何"}
    ],
    "stream": false
  }'
```

### 预期响应

如果匹配到复杂提示词，响应会包含：

```json
{
  "choices": [{
    "message": {
      "content": "# 📊 电费情况综合分析报告\n\n## 📈 数据概览\n..."
    }
  }],
  "used_complex_prompt": true,
  "complex_prompt_info": {
    "id": "xxx",
    "title": "电费情况综合分析",
    "similarity": 0.85
  }
}
```

## 📋 示例复杂提示词

系统预置了以下示例提示词：

### 1. 电费情况综合分析
- **触发词**: 电费情况、电费分析、电费统计
- **功能**: 多维度电费分析，包括趋势、构成、优化建议

### 2. 铁塔服务费深度分析
- **触发词**: 铁塔服务费、塔类服务费、铁塔费用
- **功能**: 服务费构成分析、成本优化建议

### 3. 关键指标对比分析
- **触发词**: 指标对比、指标分析、数据对比
- **功能**: 多指标对比、综合评分、改进建议

### 4. 数据异常检测与分析
- **触发词**: 异常数据、数据异常、异常分析
- **功能**: 异常检测、原因分析、解决方案

## 🔧 自定义配置

### 添加新的处理流程

1. 在管理界面创建新的复杂提示词
2. 定义触发关键词
3. 设计处理步骤序列
4. 指定回复格式模板

### 省份数据库映射

在`.env`文件中修改`PROVINCE_DATABASE_MAPPING`：

```bash
PROVINCE_DATABASE_MAPPING=GZ:analysis_gz:贵州,BJ:analysis_bj:北京,新省份:新数据库:新名称
```

## 📊 系统监控

### 日志查看

系统会记录详细的匹配和执行日志：

```
🔍 [向量搜索] 搜索查询: 当前电费情况如何
✅ [向量搜索] 找到 1 个匹配结果
🎯 [向量增强Agent] 找到匹配的复杂提示词: 电费情况综合分析 (相似度: 0.850)
🎯 [数据库选择] 省份代码 GZ 映射到数据库: analysis_gz
```

### 性能指标

- 向量搜索响应时间
- 提示词匹配成功率
- 用户满意度反馈

## 🎯 预期效果

使用向量增强系统后，您的AI助手将能够：

1. **智能识别问题类型** - 根据用户问题自动选择最合适的处理流程
2. **提供结构化回答** - 按照预定义格式返回专业分析报告
3. **省份感知查询** - 自动识别省份并查询对应数据库
4. **持续优化改进** - 通过管理界面不断完善提示词库

## 🔄 工作流程对比

### 之前（统一提示词）
```
用户问题 → 统一提示词 → 通用处理 → 简单回答
```

### 现在（向量增强）
```
用户问题 → 向量检索 → 匹配专业提示词 → 复杂处理流程 → 结构化专业报告
```

## 💡 使用建议

1. **逐步完善** - 从几个核心场景开始，逐步添加更多复杂提示词
2. **测试验证** - 使用搜索测试功能验证匹配效果
3. **优化调整** - 根据实际使用效果调整关键词和处理步骤
4. **监控反馈** - 关注用户反馈，持续优化提示词质量

## 🎉 恭喜！

您的向量增强智能分析系统已经完全搭建完成！现在可以：

- ✅ 智能匹配用户问题
- ✅ 执行复杂分析流程  
- ✅ 返回结构化报告
- ✅ 省份感知查询
- ✅ 可视化管理界面

开始享受更智能、更专业的AI分析服务吧！🚀
