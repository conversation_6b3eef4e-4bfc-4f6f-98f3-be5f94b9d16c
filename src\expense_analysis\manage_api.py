#!/usr/bin/env python3
"""
费用分析API服务管理脚本
一个文件搞定启动、停止、重启和状态检查
"""
import os
import sys
import subprocess
import argparse
import signal
import time
import platform
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 尝试导入psutil，如果没有则使用简化版本
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

def check_api_status():
    """检查API服务状态"""
    print("🔍 检查API服务状态...")

    if not HAS_PSUTIL:
        # 简化版本：通过HTTP请求检查
        try:
            import requests
            response = requests.get("http://localhost:8002/health", timeout=3)
            if response.status_code == 200:
                print("✅ API服务正在运行 (HTTP检查)")
                return True
            else:
                print("❌ API服务响应异常")
                return False
        except:
            print("❌ API服务未运行 (无法连接)")
            return False

    # 完整版本：使用psutil检查端口
    port_occupied = False
    occupying_processes = []

    try:
        for conn in psutil.net_connections(kind='inet'):
            if conn.laddr.port == 8002 and conn.status == psutil.CONN_LISTEN:
                port_occupied = True
                try:
                    process = psutil.Process(conn.pid)
                    occupying_processes.append(process)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
    except Exception as e:
        print(f"⚠️ 检查端口时出错: {e}")

    if port_occupied:
        print("✅ API服务正在运行")
        print("📋 占用8002端口的进程:")
        for proc in occupying_processes:
            try:
                cmdline = ' '.join(proc.cmdline()[:3])
                print(f"  - PID {proc.pid}: {proc.name()} ({cmdline}...)")
            except:
                print(f"  - PID {proc.pid}: {proc.name()}")
        return True
    else:
        print("❌ API服务未运行")
        return False

def run_command(cmd):
    """运行系统命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def start_api_server():
    """直接启动API服务器"""
    print("🚀 启动费用分析API服务")
    print("=" * 40)
    print("📍 端口: 8002")
    print("📖 文档: http://localhost:8002/docs")
    print("🛑 停止: Ctrl+C")
    print("=" * 40)

    # 检查是否已经运行
    if check_api_status():
        print("⚠️ API服务已在运行")
        return False

    try:
        import uvicorn
        import logging

        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        # 禁用不必要的日志
        logging.getLogger("watchfiles").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)

        print("🚀 正在启动服务...")

        # 启动服务
        uvicorn.run(
            "src.expense_analysis.api.export_api:app",
            host="0.0.0.0",
            port=8002,
            reload=True,
            log_level="info",
            reload_excludes=[
                "*.log", "*.tmp", "*.cache", "__pycache__/*",
                "*.pyc", "*.pyo", ".git/*", ".vscode/*",
                "*.xlsx", "*.json", "logs/*", "*.pid"
            ]
        )

    except ImportError:
        print("❌ 错误: 未安装uvicorn")
        print("请运行: pip install uvicorn")
        return False
    except KeyboardInterrupt:
        print("\n👋 收到中断信号，正在停止...")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def stop_api():
    """停止API服务"""
    print("🛑 停止API服务...")

    if not check_api_status():
        print("✅ API服务未运行")
        return True

    # 根据系统类型选择停止方法
    system = platform.system().lower()

    if system == "windows":
        return stop_api_windows()
    else:
        return stop_api_unix()

def stop_api_windows():
    """Windows下停止API服务"""
    print("🔍 在Windows系统中查找进程...")

    # 查找占用8002端口的进程
    success, output, error = run_command("netstat -ano | findstr :8002")

    if success and output:
        print("📋 找到占用8002端口的连接:")
        lines = output.strip().split('\n')
        pids = set()

        for line in lines:
            parts = line.split()
            if len(parts) >= 5 and ':8002' in parts[1]:
                pid = parts[-1]
                if pid != '0':  # 排除系统进程
                    pids.add(pid)
                    print(f"  - 连接: {parts[1]} -> PID: {pid}")

        # 终止进程
        killed_count = 0
        for pid in pids:
            print(f"🔄 终止进程 PID: {pid}")
            success, _, _ = run_command(f"taskkill /F /PID {pid}")
            if success:
                print(f"✅ 成功终止进程 {pid}")
                killed_count += 1
            else:
                print(f"❌ 无法终止进程 {pid}")

        return killed_count > 0
    else:
        print("✅ 未找到占用8002端口的进程")
        return True

def stop_api_unix():
    """Unix/Linux/macOS下停止API服务"""
    print("🔍 在Unix系统中查找进程...")

    # 查找占用8002端口的进程
    success, output, error = run_command("lsof -ti:8002")

    if success and output:
        pids = output.strip().split('\n')
        print(f"📋 找到 {len(pids)} 个占用8002端口的进程:")

        killed_count = 0
        for pid in pids:
            if pid.strip():
                print(f"🔄 终止进程 PID: {pid}")
                # 先尝试优雅终止
                success, _, _ = run_command(f"kill {pid}")
                if success:
                    print(f"✅ 成功终止进程 {pid}")
                    killed_count += 1
                else:
                    # 强制终止
                    print(f"⚡ 强制终止进程 {pid}")
                    success, _, _ = run_command(f"kill -9 {pid}")
                    if success:
                        killed_count += 1

        return killed_count > 0
    else:
        print("✅ 未找到占用8002端口的进程")
        return True

def restart_api():
    """重启API服务"""
    print("🔄 重启API服务...")

    # 先停止
    if not stop_api():
        print("❌ 停止服务失败")
        return False

    # 等待一下
    print("⏳ 等待服务完全停止...")
    time.sleep(3)

    # 再启动
    return start_api_server()

def show_logs():
    """显示日志（如果有的话）"""
    print("📋 查看API日志...")
    
    log_files = [
        "api.log",
        "uvicorn.log",
        "expense_analysis.log"
    ]
    
    found_logs = False
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            found_logs = True
            print(f"\n📄 {log_file}:")
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 显示最后20行
                    for line in lines[-20:]:
                        print(f"  {line.rstrip()}")
            except Exception as e:
                print(f"  ❌ 读取日志失败: {e}")
    
    if not found_logs:
        print("ℹ️ 未找到日志文件")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="费用分析API服务管理工具")
    parser.add_argument("action", choices=["start", "stop", "restart", "status", "logs"],
                       help="操作类型")
    parser.add_argument("--dev", action="store_true", 
                       help="使用开发模式启动（默认使用安全模式）")
    
    args = parser.parse_args()
    
    print("🔧 费用分析API服务管理工具")
    print("=" * 40)
    
    if args.action == "status":
        check_api_status()
    elif args.action == "start":
        start_api_server()
    elif args.action == "stop":
        stop_api()
    elif args.action == "restart":
        restart_api()
    elif args.action == "logs":
        show_logs()

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🔧 费用分析API服务管理工具")
        print("=" * 40)
        print("使用方法:")
        print("  python manage_api.py start     # 启动服务")
        print("  python manage_api.py stop      # 停止服务")
        print("  python manage_api.py restart   # 重启服务")
        print("  python manage_api.py status    # 检查状态")
        print("  python manage_api.py logs      # 查看日志")
        print("\n💡 一个文件搞定所有操作，支持优雅启动和停止")
    else:
        main()
