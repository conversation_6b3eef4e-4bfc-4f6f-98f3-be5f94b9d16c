# Dify 集成指南

## 概述

这个项目提供了一个 OpenAI 兼容的 API 服务器，可以将您的本地模型（如 Qwen3_30B_A3B）封装为标准的 OpenAI API 格式，让您能够在 Dify 中像使用 OpenAI 模型一样使用您的本地模型。

## 功能特性

✅ **完全兼容 OpenAI API 格式**
- `/v1/chat/completions` - 聊天完成接口
- `/v1/models` - 模型列表接口  
- `/v1/embeddings` - 嵌入向量接口（可选）

✅ **支持流式和非流式响应**
- 支持实时流式输出
- 兼容 Dify 的流式对话需求

✅ **跨域支持 (CORS)**
- 支持来自不同域的请求
- 方便前端应用集成

✅ **健康检查**
- `/health` 端点用于服务状态监控

## 快速开始

### 1. 配置环境

确保您的 `.env` 文件包含必要的配置：

```bash
# 本地LLM配置（必需）
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_api_key_here
LOCAL_LLM_MODEL_URL=http://localhost:9997/v1/chat/completions
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B

# API服务器配置（可选）
API_SERVER_HOST=0.0.0.0
API_SERVER_PORT=8000

# BGE嵌入模型配置（可选）
LOCAL_BGE_ENABLED=false
LOCAL_BGE_API_URL=http://localhost:9997/v1/embeddings
LOCAL_BGE_MODEL_NAME=bge-large-zh-v1.5
```

### 2. 启动服务器

#### 方法一：使用快速启动脚本
```bash
python start_dify_compatible_server.py
```

#### 方法二：直接运行服务器
```bash
python examples/openai_compatible_server.py
```

### 3. 验证服务

启动后，您可以通过以下端点验证服务：

- **健康检查**: http://localhost:8000/health
- **模型列表**: http://localhost:8000/v1/models
- **API文档**: http://localhost:8000/docs

## 在 Dify 中配置

### 1. 进入模型提供商设置

在 Dify 管理界面中：
1. 进入 **设置** → **模型提供商**
2. 选择 **OpenAI** 或添加 **自定义模型提供商**

### 2. 配置连接信息

```
API 端点: http://localhost:8000/v1
API 密钥: any-key-works（本地服务不验证密钥）
模型名称: Qwen3_30B_A3B
```

### 3. 测试连接

点击 **测试连接** 按钮验证配置是否正确。

## API 使用示例

### 聊天完成 (Chat Completions)

```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer any-key" \
  -d '{
    "model": "Qwen3_30B_A3B",
    "messages": [
      {"role": "user", "content": "你好，请介绍一下自己"}
    ],
    "temperature": 0.7,
    "stream": false
  }'
```

### 流式聊天

```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer any-key" \
  -d '{
    "model": "Qwen3_30B_A3B",
    "messages": [
      {"role": "user", "content": "写一首关于春天的诗"}
    ],
    "temperature": 0.7,
    "stream": true
  }'
```

### 嵌入向量（如果启用了BGE）

```bash
curl -X POST "http://localhost:8000/v1/embeddings" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer any-key" \
  -d '{
    "model": "bge-large-zh-v1.5",
    "input": ["这是一个测试文本", "这是另一个测试文本"]
  }'
```

## 高级配置

### 端口和主机配置

在 `.env` 文件中修改：

```bash
API_SERVER_HOST=0.0.0.0  # 监听所有网络接口
API_SERVER_PORT=8000     # 自定义端口
```

### 外网访问

如果需要从外网访问（例如 Dify 部署在其他服务器上）：

1. 确保防火墙开放对应端口
2. 将 `API_SERVER_HOST` 设置为 `0.0.0.0`
3. 在 Dify 中使用外网IP地址：`http://your-server-ip:8000/v1`

### HTTPS 支持

对于生产环境，建议使用 nginx 等反向代理来提供 HTTPS：

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    # SSL 配置...
    
    location /v1/ {
        proxy_pass http://localhost:8000/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 故障排除

### 常见问题

1. **连接失败**
   - 检查本地模型服务是否正在运行
   - 验证 URL 和端口是否正确
   - 查看防火墙设置

2. **模型初始化失败**
   - 检查 `.env` 配置文件
   - 确认本地模型 API 可访问
   - 查看控制台错误信息

3. **Dify 连接超时**
   - 增加 Dify 的请求超时时间
   - 检查网络延迟
   - 确认服务器资源充足

### 日志监控

服务器会输出详细的日志信息，包括：
- 模型初始化状态
- API 请求和响应
- 错误信息和堆栈跟踪

### 性能优化

1. **并发处理**: FastAPI 支持异步处理，可以同时处理多个请求
2. **资源监控**: 监控 CPU、内存和GPU使用情况
3. **缓存策略**: 可以添加响应缓存来提高性能

## 支持的功能

| 功能 | 状态 | 说明 |
|------|------|------|
| 基础聊天 | ✅ | 完全支持 |
| 流式输出 | ✅ | 模拟流式（逐词输出） |
| 多轮对话 | ✅ | 通过消息历史实现 |
| 系统提示 | ✅ | 支持 system role |
| 温度控制 | ✅ | 传递给底层模型 |
| Token 统计 | ✅ | 估算实现 |
| 嵌入向量 | ✅ | 需要启用 BGE |
| 函数调用 | ❌ | 暂不支持 |
| 图像理解 | ❌ | 暂不支持 |

## 更新日志

- **v1.0.0**: 初始版本，支持基础聊天完成和嵌入功能
- 支持 OpenAI API 兼容格式
- 集成 FastAPI 异步服务器
- 添加 CORS 和健康检查支持

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！ 