# 安装指南

## 依赖安装问题解决方案

### 问题描述
在全新的 Python 环境中安装 `requirements.txt` 时可能遇到依赖冲突，特别是 numpy 和 streamlit 版本冲突。

### 解决方案

#### 方案 1：分步安装（推荐）

```bash
# 1. 首先安装核心依赖
pip install -r requirements-core.txt

# 2. 然后安装其他依赖
pip install streamlit fastapi uvicorn

# 3. 最后安装向量数据库相关
pip install chromadb faiss-cpu sentence-transformers
```

#### 方案 2：使用更新的 requirements.txt

我们已经修复了依赖冲突，现在可以直接安装：

```bash
pip install -r requirements.txt
```

#### 方案 3：最小化安装

如果仍有问题，使用最小依赖：

```bash
# 只安装核心功能
pip install langchain langchain-openai python-dotenv openai

# 按需添加其他组件
pip install streamlit  # 用于 Web UI
pip install fastapi uvicorn  # 用于 API 服务
pip install chromadb  # 用于向量存储
```

### 常见冲突解决

#### NumPy 版本冲突
```bash
# 如果遇到 numpy 冲突，先卸载再重装
pip uninstall numpy -y
pip install "numpy>=1.26.2,<2.0.0"
```

#### Streamlit 版本问题
```bash
# 使用支持新版 numpy 的 streamlit
pip install "streamlit>=1.35.0"
```

### 验证安装

安装完成后，运行以下命令验证：

```bash
python -c "import langchain; print('LangChain 安装成功')"
python -c "import openai; print('OpenAI 安装成功')"
python -c "import streamlit; print('Streamlit 安装成功')"
```

### 环境要求

- Python 3.8+
- pip 最新版本
- 建议使用虚拟环境：

```bash
python -m venv langchain_env
# Windows
langchain_env\Scripts\activate
# Linux/Mac
source langchain_env/bin/activate
```

### 故障排除

如果仍有问题，请尝试：

1. 更新 pip：`pip install --upgrade pip`
2. 清除缓存：`pip cache purge`
3. 使用 conda 环境替代 pip 虚拟环境
4. 查看具体错误信息并按提示操作 