#!/usr/bin/env python3
"""
费用分类器 - 主程序
集成所有优化：预筛选、no_think、自适应批量、多线程并发等
"""
import sys
import os
import argparse
import time
import threading
import queue
import concurrent.futures
import random
from typing import List, Dict, Tuple

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.expense_analysis.services.data_service import DataService
from src.expense_analysis.agents import ExpenseClassificationAgent
from src.expense_analysis.config.performance_config import get_config
from src.expense_analysis.models.expense_models import ExpenseRecord

# 全局变量用于自适应批次大小和频率控制
adaptive_batch_sizes = {}  # 线程ID -> 当前批次大小
request_lock = threading.Lock()  # 全局请求锁
last_request_time = 0  # 上次请求时间
current_request_delay = 0.3  # 当前请求间隔，会从配置文件动态设置

def set_request_delay(delay_seconds):
    """设置全局请求延迟"""
    global current_request_delay
    current_request_delay = delay_seconds
    print(f"🛡️ 设置API请求间隔: {delay_seconds}秒")

def rate_limit_request():
    """全局请求频率控制"""
    global last_request_time
    with request_lock:
        current_time = time.time()
        time_since_last = current_time - last_request_time
        if time_since_last < current_request_delay:
            sleep_time = current_request_delay - time_since_last
            # 添加随机抖动避免同时请求
            sleep_time += random.uniform(0, 0.2)
            time.sleep(sleep_time)
        last_request_time = time.time()

class ExpenseClassifier:
    """费用分类器 - 集成所有优化"""

    def __init__(self, llm_provider: str = "local", performance_preset: str = "balanced", model_choice: str = "balanced"):
        self.perf_config = get_config(performance_preset)  # 性能配置

        # 使用配置中的超时时间创建LLM
        ai_timeout = self.perf_config.get("ai_timeout", 120)
        self.agent = ExpenseClassificationAgent(llm_provider, ai_timeout, model_choice)
        self.data_service = DataService()
        self.db_lock = threading.Lock()  # 数据库写入锁
        self.performance_preset = performance_preset
        self.model_choice = model_choice
    
    def classify_all(self, table_name: str, filters: dict = None, clear_data: bool = False):
        """智能分类处理 - 集成所有优化"""
        print("🚀 启动智能费用分类")

        # 保存目标月份，供其他方法使用
        if filters and filters.get('rpt_month'):
            self._target_month = filters['rpt_month']
            print(f"🎯 目标月份: {self._target_month}")

        # 可选：清理已有数据
        if clear_data:
            self._clear_existing_data(filters)
        
        # 步骤1：获取所有待处理记录
        all_records = self._get_all_records(table_name, filters)

        # 步骤2：分离有备注和无备注的记录
        if all_records:
            self._process_records_intelligently(all_records)
        
        print("🎉 智能分类完成！")

    def _get_all_records(self, table_name: str, filters: dict = None) -> List:
        """获取所有待处理记录"""
        print("📊 获取待处理记录...")

        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()

            # 基础SQL
            base_sql = f"""
            SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                   reg_id, reg_name, billamount_date, paymentdetail_note,
                   mandatory_note, search_keywords, auditing_state,
                   last_review_result, last_review_comment, billamount_startdate, billamount_enddate
            FROM {table_name}
            WHERE billaccountpaymentdetail_id NOT IN (
                SELECT billaccountpaymentdetail_id FROM ele_payment_ai_sort
            )
            """

            # 添加过滤条件
            params = []
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            base_sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            params.append(year_month)
                        elif key == 'preg_id':
                            base_sql += " AND preg_id = %s"
                            params.append(value)

            cursor.execute(base_sql, params)
            all_records = cursor.fetchall()

            print(f"📋 待处理记录: {len(all_records)} 条")

            return all_records

    def _process_records_intelligently(self, records: List):
        """智能处理记录：分离有备注和无备注的记录"""
        print("🧠 智能分析记录...")

        # 分离有备注和无备注的记录
        records_with_remarks = []
        records_without_remarks = []

        for row in records:
            # 从tuple中提取字段 (根据_get_all_records的SELECT顺序)
            # SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
            #        reg_id, reg_name, billamount_date, paymentdetail_note,
            #        mandatory_note, search_keywords, auditing_state,
            #        last_review_result, last_review_comment, billamount_startdate, billamount_enddate

            search_keywords = row[9] if len(row) > 9 else None
            mandatory_note = row[8] if len(row) > 8 else None
            paymentdetail_note = row[7] if len(row) > 7 else None

            # 检查是否有有效备注内容
            has_content = False

            # 检查关键字
            if (search_keywords and str(search_keywords).strip() and
                str(search_keywords).strip() not in ['null', '无', '', '[]']):
                has_content = True

            # 检查必填备注
            if (mandatory_note and str(mandatory_note).strip() and
                str(mandatory_note).strip() not in ['null', '无', '', '[]']):
                has_content = True

            # 检查备注（包括JSON格式）
            if paymentdetail_note and str(paymentdetail_note).strip():
                raw_note = str(paymentdetail_note).strip()
                if raw_note not in ['null', '无', '', '[]']:
                    try:
                        import json
                        if raw_note.startswith('[') and raw_note.endswith(']'):
                            json_data = json.loads(raw_note)
                            for item in json_data:
                                if isinstance(item, dict) and 'content' in item:
                                    content = item.get('content', '').strip()
                                    if content and content not in ['null', '无', '', '0']:
                                        has_content = True
                                        break
                        else:
                            has_content = True
                    except:
                        has_content = True

            if has_content:
                records_with_remarks.append(row)
            else:
                records_without_remarks.append(row)

        print(f"📊 记录分类结果:")
        print(f"   有备注记录: {len(records_with_remarks)} 条 (需要AI分类)")
        print(f"   无备注记录: {len(records_without_remarks)} 条 (直接入库)")

        # 处理无备注记录 - 直接入库
        if records_without_remarks:
            # 使用已保存的目标月份
            target_month = getattr(self, '_target_month', None)
            self._save_no_remark_records(records_without_remarks, target_month)

        # 处理有备注记录 - AI分类 (使用实时保存)
        if records_with_remarks:
            self._ai_process_records_with_realtime_save(records_with_remarks)

    def _save_no_remark_records(self, records: List, target_month: str = None):
        """直接保存无备注记录，不需要AI分类"""
        print(f"💾 直接保存 {len(records)} 条无备注记录...")

        insert_data = []
        for row in records:
            # 使用用户指定的月份，而不是硬编码
            rpt_month = target_month or '202302'  # 如果没有指定，使用默认值

            # 从tuple中提取字段
            insert_data.append((
                row[0],  # billaccountpaymentdetail_id
                row[1],  # payment_code
                row[2],  # preg_id
                row[3],  # preg_name
                row[4],  # reg_id
                row[5],  # reg_name
                rpt_month,  # 使用用户输入的月份
                '无备注信息',
                '无备注',
                0.9,  # 高置信度，因为确实无备注
                row[13] if len(row) > 13 else None,  # billamount_startdate
                row[14] if len(row) > 14 else None,  # billamount_enddate
                row[10] if len(row) > 10 else None   # auditing_state (索引10)
            ))

        # 高效批量插入 - 分批处理避免内存问题
        batch_size = 1000  # 每批1000条
        total_saved = 0

        print(f"🔍 准备插入数据: {len(insert_data)} 条")
        print(f"📝 示例数据: {insert_data[0] if insert_data else 'None'}")

        try:
            print("🔗 获取数据库连接...")
            with self.data_service.get_connection() as conn:
                print("✅ 数据库连接成功")
                cursor = conn.cursor()

                print(f"📦 开始真正的批量插入，每批 {batch_size} 条...")

                # 真正的批量插入 - 一个SQL包含多个VALUES
                for i in range(0, len(insert_data), batch_size):
                    batch = insert_data[i:i + batch_size]
                    print(f"🚀 执行批次 {i//batch_size + 1}，大小: {len(batch)} 条")

                    # 构建包含多个VALUES的SQL
                    values_placeholder = "(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s)"
                    values_list = ", ".join([values_placeholder] * len(batch))

                    sql = f"""
                    INSERT INTO ele_payment_ai_sort
                    (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                     reg_id, reg_name, rpt_month, original_remark, ai_category,
                     ai_confidence, ai_classified_time, billamount_startdate, billamount_enddate, auditing_state)
                    VALUES {values_list}
                    ON DUPLICATE KEY UPDATE
                    ai_category = VALUES(ai_category),
                    ai_confidence = VALUES(ai_confidence),
                    ai_classified_time = VALUES(ai_classified_time),
                    original_remark = VALUES(original_remark),
                    billamount_startdate = VALUES(billamount_startdate),
                    billamount_enddate = VALUES(billamount_enddate),
                    auditing_state = VALUES(auditing_state)
                    """

                    # 展平批次数据为一维列表
                    flat_data = []
                    for row in batch:
                        flat_data.extend(row)

                    start_time = time.time()
                    cursor.execute(sql, flat_data)
                    exec_time = time.time() - start_time

                    total_saved += len(batch)
                    print(f"💾 真正批量保存进度: {total_saved}/{len(insert_data)} 条 (用时: {exec_time:.2f}秒)")

                print(f"✅ 已保存 {total_saved} 条无备注记录")

        except Exception as e:
            print(f"❌ 保存无备注记录失败: {str(e)}")
            import traceback
            print(f"🔍 详细错误: {traceback.format_exc()}")

    def _ai_process_records_with_remarks(self, records: List):
        """AI处理所有记录"""
        print(f"🤖 AI处理 {len(records)} 条记录...")

        # 转换为ExpenseRecord对象
        expense_records = []
        for row in records:
            if isinstance(row, tuple):
                # 数据库查询返回的tuple格式
                record = ExpenseRecord(
                    billaccountpaymentdetail_id=row[0],
                    payment_code=row[1],
                    preg_id=row[2],
                    preg_name=row[3],
                    reg_id=row[4],
                    reg_name=row[5],
                    billamount_date=row[6],
                    paymentdetail_note=row[7],
                    mandatory_note=row[8],
                    search_keywords=row[9],
                    auditing_state=row[10],
                    last_review_result=row[11],
                    last_review_comment=row[12],
                    rpt_month=None,  # 从日期字段推导
                    billamount_startdate=row[13],
                    billamount_enddate=row[14]
                )
            else:
                # 已经是ExpenseRecord对象
                record = row
            expense_records.append(record)

        # 提取备注信息
        start_time = time.time()
        comprehensive_remarks = self.data_service.get_comprehensive_remarks_for_classification(expense_records)

        print(f"📝 提取到 {len(comprehensive_remarks)} 条记录")

        # 加载分类类别
        categories = self._load_categories()

        # 使用超高速多线程并发分类
        classification_results = self._hyper_speed_concurrent_classify(comprehensive_remarks, categories, expense_records)

        ai_duration = time.time() - start_time
        ai_speed = len(comprehensive_remarks) / ai_duration if ai_duration > 0 else 0

        print(f"✅ AI处理完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   ⏱️  用时: {ai_duration/60:.1f}分钟")
        print(f"   🚀 速度: {ai_speed:.1f} 条/秒")

        # 保存AI分类结果
        self._save_ai_results(expense_records, classification_results)

    def _ai_process_records_with_realtime_save(self, records: List):
        """AI处理有备注记录 - 实时保存版本"""
        print(f"🤖 AI处理 {len(records)} 条有备注记录 (实时保存)...")

        # 转换为ExpenseRecord对象
        expense_records = []
        for row in records:
            if isinstance(row, tuple):
                record = ExpenseRecord(
                    billaccountpaymentdetail_id=row[0],
                    payment_code=row[1],
                    preg_id=row[2],
                    preg_name=row[3],
                    reg_id=row[4],
                    reg_name=row[5],
                    billamount_date=row[6],
                    paymentdetail_note=row[7],
                    mandatory_note=row[8],
                    search_keywords=row[9],
                    auditing_state=row[10],
                    last_review_result=row[11],
                    last_review_comment=row[12],
                    rpt_month=None,  # 从日期字段推导
                    billamount_startdate=row[13],
                    billamount_enddate=row[14]
                )
            else:
                record = row
            expense_records.append(record)

        # 提取备注信息
        start_time = time.time()
        comprehensive_remarks = self.data_service.get_comprehensive_remarks_for_classification(expense_records)

        print(f"📝 提取到 {len(comprehensive_remarks)} 条有备注记录")

        # 加载分类类别
        categories = self._load_categories()

        # 使用实时保存的超高速多线程并发分类
        self._hyper_speed_concurrent_classify(comprehensive_remarks, categories, expense_records)

        ai_duration = time.time() - start_time
        ai_speed = len(comprehensive_remarks) / ai_duration if ai_duration > 0 else 0

        print(f"✅ AI处理完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   ⏱️  用时: {ai_duration/60:.1f}分钟")
        print(f"   🚀 速度: {ai_speed:.1f} 条/秒")
        print(f"   💾 实时保存: 每个批次完成后立即保存到数据库")

    def _clear_existing_data(self, filters: dict = None):
        """清理已有分类数据"""
        print("🧹 清理已有分类数据...")

        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()

            if filters and filters.get('rpt_month'):
                # 只清理指定月份的数据
                sql = "DELETE FROM ele_payment_ai_sort WHERE rpt_month = %s"
                cursor.execute(sql, (filters['rpt_month'],))
                deleted_count = cursor.rowcount
                print(f"✅ 已清理 {filters['rpt_month']} 月份的 {deleted_count} 条记录")
            else:
                # 清理所有数据（谨慎使用）
                confirm = input("⚠️ 确认清理所有分类数据？(输入 'YES' 确认): ")
                if confirm == 'YES':
                    sql = "DELETE FROM ele_payment_ai_sort"
                    cursor.execute(sql)
                    deleted_count = cursor.rowcount
                    print(f"✅ 已清理所有 {deleted_count} 条记录")
                else:
                    print("❌ 取消清理操作")
                    return

            conn.commit()
    
    def _presort_records(self, table_name: str, filters: dict = None) -> Tuple[List, List]:
        """预筛选记录：分离无备注和有备注"""
        print("📊 预筛选记录...")
        
        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            # 基础SQL
            base_sql = f"""
            SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                   reg_id, reg_name, billamount_date, paymentdetail_note,
                   mandatory_note, search_keywords, auditing_state,
                   last_review_result, last_review_comment, billamount_startdate, billamount_enddate
            FROM {table_name}
            WHERE billaccountpaymentdetail_id NOT IN (
                SELECT billaccountpaymentdetail_id FROM ele_payment_ai_sort
            )
            """
            
            params = []
            
            # 添加过滤条件
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            base_sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            params.append(year_month)
                        elif key == 'preg_id':
                            base_sql += " AND preg_id = %s"
                            params.append(value)
            
            # 不限制数量，处理所有记录
            
            cursor.execute(base_sql, params)
            all_records = cursor.fetchall()

            # 同时查询总记录数用于对比
            # 构建总记录数查询
            total_sql = f"""
            SELECT count(1)
            FROM {table_name}
            WHERE 1=1
            """
            total_params = []
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            total_sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            total_params.append(year_month)
                        elif key == 'preg_id':
                            total_sql += " AND preg_id = %s"
                            total_params.append(value)

            cursor.execute(total_sql, total_params)
            total_records = cursor.fetchone()[0]

            print(f"📋 月份总记录: {total_records} 条")
            print(f"📋 待处理记录: {len(all_records)} 条")
            print(f"📋 已处理记录: {total_records - len(all_records)} 条")
            
            # 分离无备注和有备注记录
            no_remark_records = []
            with_remark_records = []
            
            for row in all_records:
                paymentdetail_note = row[7]  # paymentdetail_note字段
                mandatory_note = row[8]      # mandatory_note字段
                
                # 判断是否有有效备注
                has_remark = False
                
                # 检查paymentdetail_note (放宽条件)
                if paymentdetail_note and paymentdetail_note.strip():
                    note = paymentdetail_note.strip()
                    # 只排除明显无效的内容，保留更多备注
                    if (note != '[]' and note != 'null' and note != '无' and
                        note != '退回' and note != '0' and note != '1' and
                        len(note) > 1):  # 从3改为1，保留更多短备注
                        has_remark = True

                # 检查mandatory_note (放宽条件)
                if not has_remark and mandatory_note and mandatory_note.strip():
                    note = mandatory_note.strip()
                    # 只排除明显无效的内容
                    if (note != 'null' and note != '无' and note != '退回' and
                        len(note) > 1):  # 从3改为1
                        has_remark = True
                
                if has_remark:
                    with_remark_records.append(row)
                else:
                    no_remark_records.append(row)
            
            print(f"📊 预筛选结果:")
            print(f"   📝 有备注记录: {len(with_remark_records)} 条 ({len(with_remark_records)/len(all_records)*100:.1f}%)")
            print(f"   📄 无备注记录: {len(no_remark_records)} 条 ({len(no_remark_records)/len(all_records)*100:.1f}%)")
            
            return no_remark_records, with_remark_records
    
    def _batch_process_no_remark(self, no_remark_records: List):
        """批量处理无备注记录（毫秒级）"""
        if not no_remark_records:
            print("📄 没有无备注记录需要处理")
            return
        
        print(f"⚡ 批量处理 {len(no_remark_records)} 条无备注记录...")
        
        # 确保分类结果表存在
        self.data_service._ensure_classification_table()
        
        start_time = time.time()
        
        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()
            
            # 准备批量插入数据
            insert_data = []
            current_time = time.strftime('%Y-%m-%d %H:%M:%S')
            
            for row in no_remark_records:
                # 计算rpt_month
                billamount_startdate = row[13]  # billamount_startdate
                if billamount_startdate:
                    rpt_month = billamount_startdate.strftime('%Y%m')
                else:
                    rpt_month = None
                
                insert_data.append((
                    row[0],  # billaccountpaymentdetail_id
                    row[1],  # payment_code
                    row[2],  # preg_id
                    row[3],  # preg_name
                    row[4],  # reg_id
                    row[5],  # reg_name
                    rpt_month,
                    '',      # original_remark (空)
                    '无备注', # ai_category
                    1.0,     # ai_confidence (100%确定)
                    current_time,
                    row[13], # billamount_startdate
                    row[14]  # billamount_enddate
                ))
            
            # 批量插入
            sql = """
            INSERT INTO ele_payment_ai_sort
            (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
             reg_id, reg_name, rpt_month, original_remark, ai_category,
             ai_confidence, ai_classified_time, billamount_startdate, billamount_enddate)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            ai_category = VALUES(ai_category),
            ai_confidence = VALUES(ai_confidence),
            ai_classified_time = VALUES(ai_classified_time),
            billamount_startdate = VALUES(billamount_startdate),
            billamount_enddate = VALUES(billamount_enddate)
            """
            
            cursor.executemany(sql, insert_data)
            conn.commit()
        
        duration = time.time() - start_time
        speed = len(no_remark_records) / duration
        
        print(f"✅ 无备注记录处理完成:")
        print(f"   📊 处理数量: {len(no_remark_records)} 条")
        print(f"   ⏱️  用时: {duration:.2f}秒")
        print(f"   🚀 速度: {speed:.0f} 条/秒")
    
    def _ai_process_with_remark(self, with_remark_records: List):
        """AI处理有备注记录"""
        print(f"🤖 AI处理 {len(with_remark_records)} 条有备注记录...")
        
        # 转换为ExpenseRecord对象
        records = []
        for row in with_remark_records:
            # 计算rpt_month
            billamount_startdate = row[13]
            if billamount_startdate:
                rpt_month = billamount_startdate.strftime('%Y%m')
            else:
                rpt_month = None
            
            record = self.data_service._dict_to_expense_record(
                dict(zip([
                    'billaccountpaymentdetail_id', 'payment_code', 'preg_id', 'preg_name',
                    'reg_id', 'reg_name', 'billamount_date', 'paymentdetail_note',
                    'mandatory_note', 'search_keywords', 'auditing_state',
                    'last_review_result', 'last_review_comment', 'billamount_startdate', 'billamount_enddate'
                ], row)), 
                rpt_month
            )
            records.append(record)
        
        # 提取综合备注
        comprehensive_remarks = self.data_service.get_comprehensive_remarks_for_classification(records)
        if not comprehensive_remarks:
            print("❌ 没有提取到有效的备注内容")
            return
        
        print(f"📝 提取到 {len(comprehensive_remarks)} 条有效备注")
        
        # 加载分类类别
        categories = self._load_categories()
        
        # 使用超高速多线程并发分类
        start_time = time.time()
        print(f"🤖 开始AI分类，预计用时: {len(comprehensive_remarks)*0.1/60:.1f}分钟 (多线程加速)")

        # 超级优化：多线程并发 + 大批量 + 实时保存
        classification_results = self._hyper_speed_concurrent_classify(
            comprehensive_remarks,
            categories,
            records
        )
        
        ai_duration = time.time() - start_time
        ai_speed = len(comprehensive_remarks) / ai_duration if ai_duration > 0 else 0
        
        print(f"✅ AI处理完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   ⏱️  用时: {ai_duration/60:.1f}分钟")
        print(f"   🚀 速度: {ai_speed:.1f} 条/秒")
        
        # 保存AI分类结果
        self._save_ai_results(records, classification_results)
    
    def _load_categories(self):
        """加载分类类别"""
        try:
            import json
            categories_file = os.path.join(os.path.dirname(__file__), "discovered_categories.json")
            with open(categories_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('categories', [])
        except:
            return [
                "无备注",
                "清单序号定位",
                "数据异常处理",
                "业务流程问题",
                "其他原因"
            ]
    
    def _save_ai_results(self, records: List, classification_results: Dict):
        """保存AI分类结果 - 高效批量插入版本"""
        print("💾 保存AI分类结果...")

        # 准备批量插入数据
        insert_data = []

        for record in records:
            record_id = record.billaccountpaymentdetail_id

            if record_id in classification_results:
                result = classification_results[record_id]

                # 处理结果格式
                if hasattr(result, 'category'):
                    category = result.category
                    confidence = result.confidence
                else:
                    category = result.get('category', '其他费用')
                    confidence = result.get('confidence', 0.1)

                # 收集原始备注
                original_remarks = []
                if record.paymentdetail_note and record.paymentdetail_note.strip():
                    original_remarks.append(record.paymentdetail_note.strip())
                if record.mandatory_note and record.mandatory_note.strip():
                    original_remarks.append(f"[必填] {record.mandatory_note.strip()}")

                # 使用用户输入的月份作为默认值
                target_month = getattr(self, '_target_month', '202302')

                insert_data.append((
                    record.billaccountpaymentdetail_id,
                    record.payment_code,
                    record.preg_id,
                    record.preg_name,
                    record.reg_id,
                    record.reg_name,
                    record.rpt_month or target_month,
                    '; '.join(original_remarks) if original_remarks else '无备注信息',
                    category,
                    confidence,
                    record.billamount_startdate,
                    record.billamount_enddate,
                    record.auditing_state
                ))

        # 高效批量插入
        if insert_data:
            batch_size = 1000  # 每批1000条
            total_saved = 0

            try:
                with self.data_service.get_connection() as conn:
                    cursor = conn.cursor()

                    # 真正的批量插入 - 分批处理
                    for i in range(0, len(insert_data), batch_size):
                        batch = insert_data[i:i + batch_size]

                        # 构建包含多个VALUES的SQL
                        values_placeholder = "(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s)"
                        values_list = ", ".join([values_placeholder] * len(batch))

                        sql = f"""
                        INSERT INTO ele_payment_ai_sort
                        (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                         reg_id, reg_name, rpt_month, original_remark, ai_category,
                         ai_confidence, ai_classified_time, billamount_startdate, billamount_enddate, auditing_state)
                        VALUES {values_list}
                        ON DUPLICATE KEY UPDATE
                        ai_category = VALUES(ai_category),
                        ai_confidence = VALUES(ai_confidence),
                        ai_classified_time = VALUES(ai_classified_time),
                        original_remark = VALUES(original_remark),
                        auditing_state = VALUES(auditing_state),
                        billamount_startdate = VALUES(billamount_startdate),
                        billamount_enddate = VALUES(billamount_enddate)
                        """

                        # 展平批次数据
                        flat_data = []
                        for row in batch:
                            flat_data.extend(row)

                        cursor.execute(sql, flat_data)
                        total_saved += len(batch)
                        print(f"💾 AI结果真正批量保存进度: {total_saved}/{len(insert_data)} 条")

                    print(f"✅ 已保存 {total_saved} 条AI分类结果")

            except Exception as e:
                print(f"❌ 保存AI分类结果失败: {str(e)}")
        else:
            print("⚠️ 没有AI分类结果需要保存")

    def _turbo_classify_with_realtime_save(self, comprehensive_remarks, categories, records):
        """极速分类 + 实时保存 - 集成所有优化"""
        import time

        print(f"🚀 启动极速分类模式")
        print(f"⚡ 优化特性: no_think + 大批量 + 实时保存 + 自适应")

        # 创建记录ID到记录的映射
        record_map = {record.billaccountpaymentdetail_id: record for record in records}

        # 极速配置
        batch_size = 40  # 初始批量
        max_batch_size = 80  # 最大批量
        total_saved = 0

        print(f"🔥 极速配置:")
        print(f"   初始批量: {batch_size}")
        print(f"   最大批量: {max_batch_size}")
        print(f"   实时保存: 每批次完成后立即保存")
        print(f"   no_think: 已启用")

        i = 0
        batch_num = 1
        start_time = time.time()

        while i < len(comprehensive_remarks):
            batch = comprehensive_remarks[i:i + batch_size]

            print(f"🚀 批次 {batch_num}, 大小: {len(batch)}")

            batch_start = time.time()

            try:
                # 频率控制 - 避免429错误
                rate_limit_request()

                # AI分类
                batch_results = self.agent._classify_comprehensive_batch(batch, categories)

                batch_duration = time.time() - batch_start
                batch_speed = len(batch) / batch_duration

                print(f"✅ 批次完成 (用时: {batch_duration:.1f}秒, 速度: {batch_speed:.1f}条/秒)")

                # 立即保存这批次的结果
                batch_saved = self._save_batch_results(batch, batch_results, record_map)
                total_saved += batch_saved
                print(f"💾 已保存 {batch_saved} 条到数据库 (累计: {total_saved})")

                # 自适应批量调整
                if batch_duration < 20:  # 很快完成
                    batch_size = min(batch_size + 10, max_batch_size)
                    print(f"⚡ 性能优秀，批量增加到: {batch_size}")
                elif batch_duration < 35:  # 正常速度
                    batch_size = min(batch_size + 5, max_batch_size)
                    print(f"📈 稳步增加批量到: {batch_size}")
                elif batch_duration > 50:  # 接近超时
                    batch_size = max(batch_size - 10, 15)
                    print(f"🔽 避免超时，减少批量到: {batch_size}")
                elif batch_duration >= 60:  # 超时了
                    batch_size = max(batch_size - 15, 10)
                    print(f"⚠️ 超时，大幅减少批量到: {batch_size}")

                # 计算进度
                progress = (i + len(batch)) / len(comprehensive_remarks) * 100
                elapsed = time.time() - start_time
                if progress > 0:
                    estimated_total = elapsed / (progress / 100)
                    remaining = estimated_total - elapsed
                    print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")

            except Exception as e:
                error_msg = str(e)
                print(f"❌ 批次失败: {error_msg[:100]}...")

                # 检查是否是可重试的网络错误
                retryable_errors = ["429", "502", "503", "504", "500", "Too Many Requests",
                                  "Bad Gateway", "Service Unavailable", "Gateway Timeout",
                                  "Internal Server Error", "Connection", "Timeout", "Network"]
                is_retryable = any(err in error_msg for err in retryable_errors)

                if is_retryable:
                    print(f"🔄 遇到网络/服务错误，等待后重试: {error_msg[:50]}...")
                    time.sleep(3 + random.uniform(0, 2))  # 等待3-5秒后重试

                    # 重试一次
                    try:
                        rate_limit_request()
                        batch_results = self.agent._classify_comprehensive_batch(batch, categories)

                        batch_duration = time.time() - batch_start
                        batch_speed = len(batch) / batch_duration

                        print(f"✅ 批次重试成功 (用时: {batch_duration:.1f}秒, 速度: {batch_speed:.1f}条/秒)")

                        batch_saved = self._save_batch_results(batch, batch_results, record_map)
                        total_saved += batch_saved
                        print(f"💾 重试成功已保存 {batch_saved} 条到数据库 (累计: {total_saved})")

                        # 重试成功后继续正常的自适应逻辑
                        if batch_duration < 20:
                            batch_size = min(batch_size + 10, max_batch_size)
                            print(f"⚡ 性能优秀，批量增加到: {batch_size}")

                    except Exception as retry_e:
                        print(f"❌ 重试仍然失败: {str(retry_e)[:100]}...")
                        # 重试失败时保存默认分类
                        default_results = {}
                        for remark_info in batch:
                            default_results[remark_info['record_id']] = {
                                'category': "其他费用",
                                'confidence': 0.1,
                                'reason': f"处理失败: {error_msg[:50]}"
                            }
                        batch_saved = self._save_batch_results(batch, default_results, record_map)
                        total_saved += batch_saved
                        print(f"💾 重试失败，已保存默认分类 {batch_saved} 条")
                else:
                    # 非429错误，直接保存默认分类
                    print(f"⚠️ 使用默认分类处理批次")
                    default_results = {}
                    for remark_info in batch:
                        default_results[remark_info['record_id']] = {
                            'category': "其他费用",
                            'confidence': 0.1,
                            'reason': f"处理失败: {error_msg[:50]}"
                        }
                    batch_saved = self._save_batch_results(batch, default_results, record_map)
                    total_saved += batch_saved
                    print(f"💾 失败批次已保存默认分类 {batch_saved} 条")

            i += len(batch)
            batch_num += 1

        total_duration = time.time() - start_time
        speed = len(comprehensive_remarks) / total_duration

        print(f"🎉 极速分类完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   💾 已保存数量: {total_saved} 条")
        print(f"   ⏱️  用时: {total_duration/60:.1f}分钟")
        print(f"   🚀 速度: {speed:.1f} 条/秒")

        return {}  # 返回空字典，因为已经实时保存了

    def _hyper_speed_concurrent_classify(self, comprehensive_remarks, categories, records):
        """超高速多线程并发分类 - 终极优化版本"""
        import time
        import threading
        from concurrent.futures import ThreadPoolExecutor, as_completed

        print(f"🚀🚀🚀 启动超高速并发分类模式")
        print(f"⚡ 终极优化: 多线程并发 + 大批量(100条) + 实时保存 + no_think")

        # 创建记录ID到记录的映射
        record_map = {record.billaccountpaymentdetail_id: record for record in records}

        # 使用配置文件的超高速配置
        batch_size = self.perf_config.get("batch_size", 100)
        max_workers = self.perf_config.get("max_workers", 4)
        request_delay = self.perf_config.get("request_delay", 0.3)
        total_saved = 0
        save_lock = threading.Lock()

        # 设置全局请求延迟
        set_request_delay(request_delay)

        print(f"🔥 超高速配置:")
        print(f"   批量大小: {batch_size} 条")
        print(f"   并发线程: {max_workers} 个")
        print(f"   请求间隔: {request_delay} 秒")
        print(f"   实时保存: 多线程安全")
        print(f"   no_think: 已启用")
        print(f"   配置预设: {getattr(self, 'performance_preset', 'balanced')}")

        # 动态批次处理 - 使用工作队列，让每个线程动态获取自适应大小的批次
        print(f"📦 总记录数: {len(comprehensive_remarks)} 条，将动态分批处理")

        # 创建工作队列
        work_queue = queue.Queue()
        for remark in comprehensive_remarks:
            work_queue.put(remark)

        start_time = time.time()
        queue_lock = threading.Lock()

        # 动态批次大小管理
        adaptive_batch_sizes = {}  # 每个线程的批次大小
        performance_history = []   # 性能历史记录

        def get_adaptive_batch_size(thread_id, last_duration=None):
            """获取线程的自适应批次大小"""
            if thread_id not in adaptive_batch_sizes:
                adaptive_batch_sizes[thread_id] = batch_size  # 初始大小

            current_size = adaptive_batch_sizes[thread_id]

            if last_duration is not None:
                # 根据上次执行时间调整
                # 从配置获取最大批次大小
                max_batch_size = self.perf_config.get('max_batch_size', 150)

                if last_duration < 20:  # 很快 - 大幅增加
                    new_size = min(current_size + 20, max_batch_size)
                    print(f"🚀 线程{thread_id} 性能优秀({last_duration:.1f}s)，批次增加: {current_size} → {new_size}")
                elif last_duration < 35:  # 正常 - 适度增加
                    new_size = min(current_size + 10, max_batch_size)
                    print(f"📈 线程{thread_id} 性能良好({last_duration:.1f}s)，批次增加: {current_size} → {new_size}")
                elif last_duration < 50:  # 稍慢 - 保持不变
                    new_size = current_size
                elif last_duration < 80:  # 较慢 - 适度减少
                    new_size = max(current_size - 10, 20)
                    print(f"🔽 线程{thread_id} 性能一般({last_duration:.1f}s)，批次减少: {current_size} → {new_size}")
                else:  # 很慢 - 大幅减少
                    new_size = max(current_size - 20, 10)
                    print(f"⚠️ 线程{thread_id} 性能较差({last_duration:.1f}s)，批次大幅减少: {current_size} → {new_size}")

                adaptive_batch_sizes[thread_id] = new_size

                # 记录性能历史
                performance_history.append({
                    'thread_id': thread_id,
                    'batch_size': current_size,
                    'duration': last_duration,
                    'speed': current_size / last_duration if last_duration > 0 else 0
                })

                # 保持历史记录在合理范围内
                if len(performance_history) > 100:
                    performance_history.pop(0)

            return adaptive_batch_sizes[thread_id]

        def get_next_batch(thread_id):
            """从队列中获取下一批数据，使用自适应批次大小"""
            current_batch_size = get_adaptive_batch_size(thread_id)
            batch = []

            with queue_lock:
                for _ in range(current_batch_size):
                    if work_queue.empty():
                        break
                    batch.append(work_queue.get())

            return batch

        def process_worker():
            """工作线程函数 - 持续从队列获取并处理批次"""
            nonlocal total_saved
            thread_id = threading.current_thread().ident
            batch_num = 0

            while True:
                # 获取下一批数据
                batch = get_next_batch(thread_id)
                if not batch:  # 队列为空，退出
                    break

                batch_num += 1
                print(f"🚀 线程{thread_id} 开始处理批次 {batch_num}, 大小: {len(batch)}")

                batch_start = time.time()

                try:
                    # 频率控制 - 避免429错误
                    rate_limit_request()

                    # AI分类
                    batch_results = self.agent._classify_comprehensive_batch(batch, categories)

                    batch_duration = time.time() - batch_start
                    batch_speed = len(batch) / batch_duration

                    print(f"✅ 线程{thread_id} 批次{batch_num}完成 (用时: {batch_duration:.1f}秒, 速度: {batch_speed:.1f}条/秒)")

                    # 线程安全地保存结果
                    with save_lock:
                        batch_saved = self._save_batch_results(batch, batch_results, record_map)
                        total_saved += batch_saved
                        print(f"💾 线程{thread_id} 已保存 {batch_saved} 条到数据库 (累计: {total_saved})")

                        # 计算并显示进度
                        progress = total_saved / len(comprehensive_remarks) * 100
                        elapsed = time.time() - start_time
                        if progress > 0:
                            estimated_total = elapsed / (progress / 100)
                            remaining = estimated_total - elapsed
                            print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")

                    # 更新该线程的自适应批次大小
                    get_adaptive_batch_size(thread_id, batch_duration)

                except Exception as e:
                    error_msg = str(e)
                    print(f"❌ 线程{thread_id} 批次{batch_num}失败: {error_msg[:100]}...")

                    # 检查是否是可重试的网络错误
                    retryable_errors = ["429", "502", "503", "504", "500", "Too Many Requests",
                                      "Bad Gateway", "Service Unavailable", "Gateway Timeout",
                                      "Internal Server Error", "Connection", "Timeout", "Network"]
                    is_retryable = any(err in error_msg for err in retryable_errors)

                    if is_retryable:
                        print(f"🔄 线程{thread_id} 遇到网络/服务错误，等待后重试: {error_msg[:50]}...")
                        time.sleep(2 + random.uniform(0, 2))  # 等待2-4秒后重试

                        # 重试一次
                        try:
                            rate_limit_request()
                            batch_results = self.agent._classify_comprehensive_batch(batch, categories)

                            batch_duration = time.time() - batch_start
                            batch_speed = len(batch) / batch_duration

                            print(f"✅ 线程{thread_id} 批次{batch_num}重试成功 (用时: {batch_duration:.1f}秒, 速度: {batch_speed:.1f}条/秒)")

                            with save_lock:
                                batch_saved = self._save_batch_results(batch, batch_results, record_map)
                                total_saved += batch_saved
                                print(f"💾 线程{thread_id} 重试成功已保存 {batch_saved} 条到数据库 (累计: {total_saved})")

                                # 计算并显示进度
                                progress = total_saved / len(comprehensive_remarks) * 100
                                elapsed = time.time() - start_time
                                if progress > 0:
                                    estimated_total = elapsed / (progress / 100)
                                    remaining = estimated_total - elapsed
                                    print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")

                            # 更新自适应批次大小
                            get_adaptive_batch_size(thread_id, batch_duration)

                        except Exception as retry_e:
                            print(f"❌ 线程{thread_id} 重试仍然失败: {str(retry_e)[:100]}...")
                            # 重试失败时保存默认分类
                            default_results = {}
                            for remark_info in batch:
                                default_results[remark_info['record_id']] = {
                                    'category': "其他费用",
                                    'confidence': 0.1,
                                    'reason': f"处理失败: {error_msg[:50]}"
                                }
                            with save_lock:
                                batch_saved = self._save_batch_results(batch, default_results, record_map)
                                total_saved += batch_saved
                                print(f"💾 线程{thread_id} 重试失败，已保存默认分类 {batch_saved} 条")

                                # 计算并显示进度
                                progress = total_saved / len(comprehensive_remarks) * 100
                                elapsed = time.time() - start_time
                                if progress > 0:
                                    estimated_total = elapsed / (progress / 100)
                                    remaining = estimated_total - elapsed
                                    print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")
                    else:
                        # 非网络错误，直接保存默认分类
                        print(f"⚠️ 线程{thread_id} 使用默认分类处理批次{batch_num}")
                        default_results = {}
                        for remark_info in batch:
                            default_results[remark_info['record_id']] = {
                                'category': "其他费用",
                                'confidence': 0.1,
                                'reason': f"处理失败: {error_msg[:50]}"
                            }
                        with save_lock:
                            batch_saved = self._save_batch_results(batch, default_results, record_map)
                            total_saved += batch_saved
                            print(f"💾 线程{thread_id} 失败批次已保存默认分类 {batch_saved} 条")

                            # 计算并显示进度
                            progress = total_saved / len(comprehensive_remarks) * 100
                            elapsed = time.time() - start_time
                            if progress > 0:
                                estimated_total = elapsed / (progress / 100)
                                remaining = estimated_total - elapsed
                                print(f"📈 进度: {progress:.1f}% (预计剩余: {remaining/60:.1f}分钟)")

            return total_saved

        # 使用线程池并发处理 - 每个线程持续从队列获取工作
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交工作线程
            futures = [executor.submit(process_worker) for _ in range(max_workers)]

            # 等待所有线程完成
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception as e:
                    print(f"❌ 工作线程异常: {e}")

        total_duration = time.time() - start_time
        speed = len(comprehensive_remarks) / total_duration if total_duration > 0 else 0

        print(f"🎉🎉🎉 超高速并发分类完成:")
        print(f"   📊 处理数量: {len(comprehensive_remarks)} 条")
        print(f"   💾 已保存数量: {total_saved} 条")
        print(f"   ⏱️  用时: {total_duration/60:.1f}分钟")
        print(f"   🚀 速度: {speed:.1f} 条/秒")
        print(f"   🔥 加速比: {speed/0.8:.1f}x (相比之前0.8条/秒)")

        return {}  # 返回空字典，因为已经实时保存了

    def _save_batch_results(self, batch, batch_results, record_map):
        """保存单个批次的结果到数据库"""
        saved_count = 0

        # 准备批量插入数据
        insert_data = []

        for remark_info in batch:
            record_id = remark_info['record_id']

            if record_id in batch_results and record_id in record_map:
                result = batch_results[record_id]
                record = record_map[record_id]

                if hasattr(result, 'category'):
                    category = result.category
                    confidence = result.confidence
                else:
                    category = result.get('category', '其他费用')
                    confidence = result.get('confidence', 0.1)

                # 使用新的3字段连接格式
                combined_content = remark_info.get('combined_content', ['无备注信息'])
                original_remark = combined_content[0] if combined_content else '无备注信息'

                # 确保rpt_month有值 - 使用用户输入的月份
                target_month = getattr(self, '_target_month', '202302')
                rpt_month = record.rpt_month or target_month

                insert_data.append((
                    record.billaccountpaymentdetail_id,
                    record.payment_code,
                    record.preg_id,
                    record.preg_name,
                    record.reg_id,
                    record.reg_name,
                    rpt_month,
                    original_remark,
                    category,
                    confidence,
                    record.billamount_startdate,
                    record.billamount_enddate,
                    record.auditing_state
                ))

                saved_count += 1

        # 批量插入数据库
        if insert_data:
            try:
                with self.data_service.get_connection() as conn:
                    cursor = conn.cursor()

                    # 构建真正的批量插入SQL - 一个INSERT包含多个VALUES
                    values_placeholder = "(%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), %s, %s, %s)"
                    values_list = ", ".join([values_placeholder] * len(insert_data))

                    sql = f"""
                    INSERT INTO ele_payment_ai_sort
                    (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                     reg_id, reg_name, rpt_month, original_remark, ai_category,
                     ai_confidence, ai_classified_time, billamount_startdate, billamount_enddate, auditing_state)
                    VALUES {values_list}
                    ON DUPLICATE KEY UPDATE
                    ai_category = VALUES(ai_category),
                    ai_confidence = VALUES(ai_confidence),
                    ai_classified_time = VALUES(ai_classified_time),
                    original_remark = VALUES(original_remark),
                    billamount_startdate = VALUES(billamount_startdate),
                    billamount_enddate = VALUES(billamount_enddate)
                    """

                    # 展平数据为一维列表
                    flat_data = []
                    for row in insert_data:
                        flat_data.extend(row)

                    cursor.execute(sql, flat_data)
                    # 由于autocommit=True，不需要手动commit

                    print(f"💾 真正批量保存 {len(insert_data)} 条AI分类结果成功")

            except Exception as e:
                print(f"❌ 数据库保存失败: {str(e)}")
                saved_count = 0

        return saved_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='AI费用分类器 - 智能高效的费用原因分类',
        epilog="""
使用示例:
  # 分类202301月份数据
  python ultra_fast_classifier.py --month 202301

  # 清理并重新分类
  python ultra_fast_classifier.py --month 202301 --clear-data

  # 分类指定地市数据
  python ultra_fast_classifier.py --month 202301 --city 440100
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )

    parser.add_argument('--table', default='clean_ele_payment', help='数据表名')
    parser.add_argument('--month', required=True, help='月份 (YYYYMM格式，如202301)')
    parser.add_argument('--city', help='地市ID (可选)')
    parser.add_argument('--clear-data', action='store_true', help='清理已有分类数据')
    parser.add_argument('--llm', default='local', help='LLM提供商')
    parser.add_argument('--preset', default='balanced',
                       choices=['conservative', 'balanced', 'sequential', 'aggressive', 'step1', 'step2', 'step3', 'ultimate', 'optimal_8core', 'max_performance', 'rate_limited', 'rate_limited_fast', 'custom_high'],
                       help='性能配置预设')
    parser.add_argument('--model', default='balanced',
                       choices=['fastest', 'balanced', 'accurate', 'current'],
                       help='模型选择: fastest(7B), balanced(14B), accurate(32B), current(235B)')

    args = parser.parse_args()

    # 验证月份格式
    if len(args.month) != 6 or not args.month.isdigit():
        print("❌ 错误：月份格式应为YYYYMM，如202301")
        return

    # 构建过滤条件
    filters = {'rpt_month': args.month}
    if args.city:
        filters['preg_id'] = args.city

    # 创建分类器并执行
    classifier = ExpenseClassifier(args.llm, args.preset, args.model)
    classifier.classify_all(args.table, filters, args.clear_data)


if __name__ == '__main__':
    main()
