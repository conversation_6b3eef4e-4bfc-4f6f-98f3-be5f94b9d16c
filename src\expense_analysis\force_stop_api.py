#!/usr/bin/env python3
"""
费用分析API服务强制停止脚本
用于处理顽固的进程
"""
import os
import sys
import subprocess
import platform
import time

def run_command(cmd):
    """运行系统命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def force_kill_windows():
    """Windows下强制终止进程"""
    print("💥 Windows强制终止模式")
    
    # 1. 强制终止所有Python进程中包含expense_analysis的
    print("🔍 查找Python进程...")
    success, output, _ = run_command('tasklist /FI "IMAGENAME eq python.exe" /FO CSV')
    
    if success and output:
        lines = output.strip().split('\n')[1:]  # 跳过标题行
        for line in lines:
            if line.strip():
                parts = line.replace('"', '').split(',')
                if len(parts) >= 2:
                    pid = parts[1]
                    print(f"🔄 强制终止Python进程 PID: {pid}")
                    run_command(f"taskkill /F /PID {pid}")
    
    # 2. 强制终止占用8002端口的所有进程
    print("\n🎯 强制释放8002端口...")
    success, output, _ = run_command("netstat -ano | findstr :8002")
    
    if success and output:
        lines = output.strip().split('\n')
        pids = set()
        
        for line in lines:
            parts = line.split()
            if len(parts) >= 5 and ':8002' in parts[1]:
                pid = parts[-1]
                if pid != '0':  # 排除系统进程
                    pids.add(pid)
        
        for pid in pids:
            print(f"💥 强制终止进程 PID: {pid}")
            run_command(f"taskkill /F /PID {pid}")
            time.sleep(0.5)
    
    # 3. 终止uvicorn相关进程
    print("\n🔄 终止uvicorn进程...")
    run_command('taskkill /F /IM "uvicorn*" 2>nul')
    
    # 4. 清理conda/python相关的API进程
    print("🧹 清理conda相关进程...")
    success, output, _ = run_command('tasklist /FI "IMAGENAME eq python.exe" /FO LIST')
    
    if success and output:
        # 查找命令行包含expense_analysis的进程
        current_pid = None
        for line in output.split('\n'):
            if line.startswith('PID:'):
                current_pid = line.split(':')[1].strip()
            elif 'expense_analysis' in line and current_pid:
                print(f"🎯 终止API进程 PID: {current_pid}")
                run_command(f"taskkill /F /PID {current_pid}")

def check_final_status():
    """检查最终状态"""
    print("\n🔍 最终检查...")
    
    # 检查端口
    success, output, _ = run_command("netstat -ano | findstr :8002")
    
    if success and output.strip():
        print("⚠️ 8002端口仍被占用:")
        print(output)
        return False
    else:
        print("✅ 8002端口已完全释放")
        return True

def main():
    """主函数"""
    print("💥 费用分析API服务强制停止脚本")
    print("=" * 40)
    print("⚠️ 警告：这将强制终止所有相关进程")
    print("=" * 40)
    
    if platform.system().lower() == "windows":
        force_kill_windows()
    else:
        print("❌ 此脚本目前只支持Windows系统")
        print("💡 Linux/macOS用户请使用: killall -9 python")
        return
    
    # 等待进程完全退出
    print("\n⏳ 等待进程完全退出...")
    time.sleep(3)
    
    # 检查结果
    if check_final_status():
        print("\n✅ 强制停止成功！")
        print("💡 现在可以重新启动API服务")
    else:
        print("\n⚠️ 可能仍有进程残留")
        print("💡 建议重启系统以完全清理")

if __name__ == "__main__":
    # 确认操作
    response = input("确定要强制停止所有API相关进程吗？(y/N): ")
    if response.lower() in ['y', 'yes']:
        main()
    else:
        print("👋 操作已取消")
