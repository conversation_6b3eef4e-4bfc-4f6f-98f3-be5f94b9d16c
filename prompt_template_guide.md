# 复杂提示词模板指南

## 🎯 设计原则

1. **简洁明了** - 用最简单的语言描述流程
2. **步骤清晰** - 每个步骤一句话说明
3. **工具明确** - 明确说明何时调用哪个工具
4. **格式统一** - 使用标准的JSON格式

## 📋 基础模板

```
你是[角色]，按以下步骤执行：

1. [第一步：通常是调用integrated_sql工具获取数据]
2. [第二步：分析数据，找出关键信息]
3. [第三步：如需要，调用knowledge_search工具获取建议]
4. [第四步：输出结果格式]

工具调用格式：
```json
{"tool_name": "integrated_sql", "parameters": {"question": "[查询问题]", "target_database": "JT"}}
```

```json
{"tool_name": "knowledge_search", "parameters": {"query": "[搜索内容]"}}
```

重要：[关键注意事项]
```

## 🌟 实际示例

### 示例1：电费分析
```
你是数据分析师，按以下步骤执行：

1. 调用integrated_sql工具，切换到JT数据库，查询"本省当前电费情况如何"

2. 计算劣化指标：
   - 环比权重70%，排名权重30%，基准分50分
   - 环比：下降1%加1分，上升1%减1分（小数四舍五入）
   - 排名：上升1名加5分，下降1名减5分
   - 综合得分<50分为劣化
   - 如无劣化指标，则排名最低的指标为劣化

3. 对每个劣化指标，调用knowledge_search工具查询改进建议

4. 输出三部分：
   - 本省7个指标数据表格（指标名称、本月值、环比变动、全国排名、排名变动）
   - 劣化指标表格（指标名称、综合得分、劣化原因）
   - 改进建议（来自知识库，10-20字）

重要：每个劣化指标都要单独调用一次knowledge_search工具！

注意：工具调用的JSON格式由系统自动处理，无需在提示词中重复编写。
```

### 示例2：铁塔服务费分析
```
你是成本分析师，按以下步骤执行：

1. 调用integrated_sql工具查询"本省铁塔服务费情况"
2. 分析费用构成，找出异常高的费用项
3. 对异常费用项，调用knowledge_search工具查询优化方案
4. 输出结果：费用明细表 + 异常项目表 + 优化建议

工具调用格式：
```json
{"tool_name": "integrated_sql", "parameters": {"question": "本省铁塔服务费情况", "target_database": "JT"}}
```

```json
{"tool_name": "knowledge_search", "parameters": {"query": "费用项名称+优化方案"}}
```

重要：每个异常费用项都要单独查询优化方案！
```

### 示例3：异常检测
```
你是数据质量分析师，按以下步骤执行：

1. 调用integrated_sql工具查询"本省数据异常情况"
2. 检查数据质量，识别异常数据
3. 对每个异常类型，调用knowledge_search工具查询解决方案
4. 输出结果：数据概览表 + 异常清单表 + 解决方案

工具调用格式：
```json
{"tool_name": "integrated_sql", "parameters": {"question": "本省数据异常情况", "target_database": "JT"}}
```

```json
{"tool_name": "knowledge_search", "parameters": {"query": "异常类型+解决方案"}}
```

重要：每种异常类型都要单独查询解决方案！
```

## 🔧 产品人员使用指南

### 步骤1：确定角色
- 数据分析师
- 成本分析师  
- 质量分析师
- 业务分析师

### 步骤2：设计流程
1. **数据获取** - 调用integrated_sql工具
2. **数据分析** - 找出关键信息/问题
3. **知识查询** - 调用knowledge_search工具（可选）

注意：处理步骤只描述"做什么"，不描述"怎么输出"

### 步骤3：编写提示词
- **处理步骤**：用简单的语言描述每个执行步骤
- **回复格式**：单独定义输出的结构和格式
- **分离原则**：步骤描述"做什么"，格式描述"怎么输出"
- **避免重复**：不要在步骤中重复定义输出格式

### 步骤4：测试验证
- 测试是否能正确调用工具
- 检查输出格式是否符合预期
- 验证多次工具调用是否正常

## ⚠️ 常见错误

1. **步骤太复杂** - 每个步骤应该简单明了
2. **工具调用不明确** - 要明确说明何时调用哪个工具
3. **重复定义输出** - 不要在处理步骤中描述输出格式
4. **逻辑混乱** - 处理步骤和回复格式要分离
5. **缺少关键提示** - 要强调重要的执行要求

## 💡 最佳实践

1. **保持简洁** - 能用一句话说清楚就不要用两句话
2. **突出重点** - 用"重要"、"必须"等词强调关键要求
3. **提供示例** - 给出具体的工具调用示例
4. **测试优先** - 先写简单版本，测试通过后再优化
