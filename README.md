# AI Chat LangChain

基于LangChain的智能对话系统，支持多种语言模型、记忆管理和RAG（检索增强生成）功能，并提供Analysis Agent智能数据分析服务。

## ✨ 功能特点

- 🤖 **多模型支持**: 支持OpenAI、Anthropic、本地部署模型等多种语言模型
- 🏠 **本地模型支持**: 支持本地部署的LLM和BGE嵌入模型，数据不出网
- 💾 **灵活记忆管理**: 支持内存、Redis、MongoDB等多种存储方式
- 📚 **RAG检索增强**: 基于向量数据库的文档检索和问答
- 🔄 **多种向量存储**: 支持Chroma、FAISS、Pinecone、Weaviate等
- 📄 **文档加载**: 支持PDF、Word、TXT、CSV、JSON等多种格式
- 🌐 **Web界面**: 提供Streamlit和FastAPI两种Web接口
- 🔌 **Dify集成**: 提供OpenAI兼容API，可直接在Dify中使用本地模型
- 📊 **Analysis Agent**: 智能数据分析服务，支持SQL查询和知识库搜索
- 🛠️ **模块化设计**: 易于扩展和定制

## 📦 项目结构

```
ai-chat-langchain/
├── src/                    # 源代码目录
│   ├── config/            # 配置管理
│   ├── core/              # 核心模块
│   ├── chains/            # 聊天链实现
│   └── utils/             # 工具函数
├── examples/              # 示例应用
├── data/                  # 数据存储目录
├── requirements.txt       # Python依赖
├── .env.example          # 环境变量模板
└── README.md             # 项目文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd ai-chat-langchain

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，根据需要配置API密钥
```

### 3. 配置选项

#### 选项1: 使用云端模型 (OpenAI/Anthropic)
```bash
# 在.env文件中配置
OPENAI_API_KEY=your_openai_api_key_here
# 或者
ANTHROPIC_API_KEY=your_anthropic_api_key_here
```

#### 选项2: 使用本地模型 (推荐用于数据隐私)
```bash
# 在.env文件中配置
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_local_api_key
LOCAL_LLM_MODEL_URL=http://localhost:9997/v1/chat/completions
LOCAL_LLM_MODEL_NAME=your_model_name

# 可选：配置本地BGE嵌入模型
LOCAL_BGE_ENABLED=true
LOCAL_BGE_API_URL=http://localhost:9997/v1/embeddings
```

### 4. 运行示例

#### 简单聊天
```bash
python examples/simple_chat.py
```

#### RAG聊天
```bash
python examples/rag_chat.py
```

#### 本地模型测试
```bash
python examples/local_model_example.py
```

#### Web界面 (Streamlit)
```bash
streamlit run examples/streamlit_app.py
```

#### API服务 (FastAPI)
```bash
python examples/fastapi_app.py
# 访问 http://localhost:8000/docs 查看API文档
```

#### Dify兼容API服务器 🆕
```bash
# 启动OpenAI兼容的API服务器
python start_dify_compatible_server.py
# 或者直接运行
python examples/openai_compatible_server.py

# 测试API服务器
python test_api_server.py
```

OpenAI兼容API服务器配置：
- **API端点**: `http://localhost:8000/v1`
- **API密钥**: `any-key-works`（本地服务不验证）
- **模型名称**: 您配置的本地模型名称

详细的Dify集成指南请参考：[DIFY_INTEGRATION.md](DIFY_INTEGRATION.md)

#### Analysis Agent数据分析服务 🚀
```bash
# 启动Analysis Agent服务器
python examples/analysis_agent_server.py
# 访问 http://localhost:8001/docs 查看API文档

# 或者运行测试
python test_agent.py
```

服务器启动后，您可以在Dify中配置：
- **API端点**: `http://localhost:8001/v1`
- **API密钥**: `any-key-works`（本地服务不验证）
- **模型名称**: `analysis-agent`

Analysis Agent功能：
- 🔍 **自然语言转SQL**: 将自然语言问题转换为SQL查询
- 📊 **数据库查询**: 自动执行SQL并返回格式化结果
- 📚 **知识库搜索**: 查询业务逻辑和指标计算说明
- 📈 **表格展示**: 查询结果以美观的表格形式显示
- 🔄 **流式输出**: 实时显示查询进度

## 💻 使用示例

### 基础聊天

```python
from src.core import LLMFactory
from src.chains import ChatChain

# 使用本地模型
llm = LLMFactory.create_chat_model(
    provider="local",  # 或 "openai", "anthropic"
    temperature=0.7
)

# 创建聊天链
chat_chain = ChatChain(llm=llm)

# 开始对话
response = chat_chain.chat("你好，请介绍一下自己")
print(response)
```

### 本地RAG聊天

```python
from src.core import LLMFactory, VectorStoreManager
from src.chains import RAGChain
from src.utils import DocumentLoader

# 使用本地模型
llm = LLMFactory.create_chat_model(provider="local")
embeddings = LLMFactory.create_embeddings(provider="local_bge")

# 加载文档
doc_loader = DocumentLoader()
documents = doc_loader.load_directory("./data/documents")

# 创建向量存储
vectorstore = VectorStoreManager.create_vectorstore(
    store_type="chroma",
    embeddings=embeddings,
    documents=documents
)

# 创建RAG链
rag_chain = RAGChain(llm=llm, vectorstore=vectorstore)

# RAG对话
result = rag_chain.chat("基于文档回答我的问题")
print(result["answer"])
```

## 🔧 配置说明

### 环境变量

| 变量名 | 说明 | 必需 |
|--------|------|------|
| **本地模型配置** | | |
| `LOCAL_LLM_ENABLED` | 是否启用本地LLM | 否 |
| `LOCAL_LLM_API_KEY` | 本地LLM API密钥 | 是* |
| `LOCAL_LLM_MODEL_URL` | 本地LLM API地址 | 是* |
| `LOCAL_LLM_MODEL_NAME` | 本地LLM模型名称 | 是* |
| `LOCAL_BGE_ENABLED` | 是否启用本地BGE | 否 |
| `LOCAL_BGE_API_URL` | 本地BGE API地址 | 是** |
| **云端模型配置** | | |
| `OPENAI_API_KEY` | OpenAI API密钥 | 是*** |
| `ANTHROPIC_API_KEY` | Anthropic API密钥 | 是*** |
| **其他配置** | | |
| `LANGCHAIN_API_KEY` | LangSmith API密钥 | 否 |
| `PINECONE_API_KEY` | Pinecone API密钥 | 否 |
| `REDIS_URL` | Redis连接URL | 否 |
| `MONGODB_URL` | MongoDB连接URL | 否 |

\* 当`LOCAL_LLM_ENABLED=true`时必需  
\*\* 当`LOCAL_BGE_ENABLED=true`时必需  
\*\*\* 至少需要配置一个语言模型的API密钥

### 本地模型部署建议

#### 推荐的本地模型组合
1. **LLM模型**: Qwen、ChatGLM、Baichuan等开源中文模型
2. **BGE模型**: bge-large-zh-v1.5 (智源研究院)
3. **部署工具**: Xinference、Ollama、vLLM等

#### 配置示例
```bash
# 使用Xinference部署的本地模型
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_xinference_api_key
LOCAL_LLM_MODEL_URL=http://localhost:9997/v1/chat/completions
LOCAL_LLM_MODEL_NAME=qwen1.5-chat

LOCAL_BGE_ENABLED=true
LOCAL_BGE_API_URL=http://localhost:9997/v1/embeddings
LOCAL_BGE_MODEL_NAME=bge-large-zh-v1.5
```

### 支持的模型

#### 本地模型
- **LLM**: 任何支持OpenAI API格式的本地模型
- **Embedding**: BGE系列模型 (通过Xinference等部署)

#### 云端模型
- **OpenAI**: gpt-3.5-turbo, gpt-4, text-embedding-ada-002
- **Anthropic**: claude-3-haiku-20240307, claude-3-sonnet-20240229

## 📊 向量数据库支持

| 数据库 | 类型 | 特点 |
|--------|------|------|
| Chroma | 本地/云端 | 开源，易于使用 |
| FAISS | 本地 | Facebook开源，高性能 |
| Pinecone | 云端 | 托管服务，可扩展 |
| Weaviate | 本地/云端 | 开源，功能丰富 |

## 🔗 API接口

FastAPI服务提供以下接口：

- `POST /chat` - 简单聊天
- `POST /rag` - RAG聊天
- `GET /sessions` - 获取会话列表
- `DELETE /sessions/{id}` - 删除会话
- `POST /sessions/{id}/clear` - 清空会话记忆

## 📝 文档格式支持

- PDF (.pdf)
- Word文档 (.docx)
- 文本文件 (.txt)
- CSV文件 (.csv)
- JSON文件 (.json)
- HTML文件 (.html)
- Markdown文件 (.md)

## 🛠️ 开发指南

### 添加新的语言模型

1. 在 `src/core/llm_factory.py` 中添加新的创建方法
2. 更新 `create_chat_model` 方法支持新的提供商
3. 在配置文件中添加相应的环境变量

### 添加新的向量数据库

1. 在 `src/core/vectorstore_manager.py` 中添加新的创建方法
2. 更新 `create_vectorstore` 方法支持新的数据库类型
3. 添加相应的依赖包到 `requirements.txt`

## 🧪 测试

```bash
# 运行测试
python -m pytest tests/

# 生成覆盖率报告
python -m pytest --cov=src tests/

# 测试本地模型连接
python examples/local_model_example.py
```

## 🔒 数据隐私

使用本地模型的优势：
- 🏠 **数据不出网**: 所有处理在本地完成
- ⚡ **响应速度快**: 无网络延迟
- 💰 **成本可控**: 无按量计费
- 🔧 **高度定制**: 可根据需求调整模型

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请通过以下方式获取支持：

1. 提交 [GitHub Issue](https://github.com/your-repo/ai-chat-langchain/issues)
2. 查看 [文档](https://your-docs-url.com)
3. 加入讨论群组

## 🔄 更新日志

### v1.0.0
- 初始版本发布
- 支持基础聊天和RAG功能
- 支持本地模型部署
- 提供多种示例应用
- 完整的文档和配置 