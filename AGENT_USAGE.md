# 工具调用Agent使用指南

本项目为您提供了一个功能强大的智能Agent系统，该系统集成了千问（Qwen）模型并具备多种工具调用能力。

## 🚀 快速开始

### 1. 环境配置

首先，您需要配置环境变量。请复制`env.example`文件为`.env`：

```bash
# Windows
copy env.example .env

# Linux/Mac  
cp env.example .env
```

然后编辑`.env`文件，确保千问模型配置正确：

```bash
# 本地LLM配置 (您的Qwen3_30B_A3B模型)
LOCAL_LLM_ENABLED=true
LOCAL_LLM_API_KEY=your_api_key_here
LOCAL_LLM_MODEL_URL=your_model_url_here
LOCAL_LLM_MODEL_NAME=Qwen3_30B_A3B
LOCAL_LLM_TEMPERATURE=0.7
LOCAL_LLM_TIMEOUT=60
```

### 2. 安装依赖

确保已安装所有必要的依赖：

```bash
pip install -r requirements.txt
```

### 3. 运行Agent示例

```bash
python examples/tool_calling_agent_example.py
```

## 🔧 可用工具

Agent系统内置了以下工具：

### 1. 计算器工具 (calculator)
- **功能**: 执行数学计算
- **支持**: 基本运算、数学函数（sin、cos、sqrt等）
- **示例**: "帮我计算 (123 + 456) * 789 的结果"

### 2. 网络搜索工具 (web_search)
- **功能**: 模拟网络搜索（可扩展为真实搜索API）
- **参数**: 查询词、结果数量
- **示例**: "搜索一下关于Python编程的信息"

### 3. 文件读取工具 (file_reader)
- **功能**: 读取文件内容
- **安全**: 内置路径安全检查
- **示例**: "读取文件README.md的内容"

### 4. 文件写入工具 (file_writer)
- **功能**: 将内容写入文件
- **安全**: 内置路径安全检查，自动创建目录
- **示例**: "将文本'Hello, World!'写入文件output.txt"

### 5. SQL生成工具 (sql_generator)
- **功能**: 根据自然语言问题生成SQL查询语句
- **API**: 调用您配置的SQL生成API
- **示例**: "生成查询2025年4月电费的SQL语句"

### 6. SQL执行工具 (sql_executor)
- **功能**: 执行SQL查询语句并返回结果
- **支持**: SQLite、MySQL、PostgreSQL
- **安全**: 仅允许SELECT查询，防止危险操作
- **示例**: "执行SQL: SELECT * FROM users WHERE status = 'active'"

## 💻 使用方式

### 方式一：直接运行示例

```bash
python examples/tool_calling_agent_example.py
```

选择"交互式聊天"模式，然后您可以：

- 直接对话：Agent会智能判断是否需要使用工具
- 使用特殊命令：
  - `stats` - 查看统计信息
  - `tools` - 查看可用工具
  - `clear` - 清空对话历史
  - `export` - 导出对话记录
  - `quit` - 退出程序

### 方式二：编程方式使用

```python
from src.core import LLMFactory
from src.agents import ToolCallingAgent, AgentExecutor

# 创建千问模型
llm = LLMFactory.create_chat_model(provider="local", temperature=0.7)

# 创建Agent
agent = ToolCallingAgent(llm=llm)
executor = AgentExecutor(agent=agent)

# 与Agent对话
result = executor.chat("帮我计算 2 + 3 * 4 的结果")
print(result['response'])

# 检查是否使用了工具
if result['tool_used']:
    print(f"使用了工具: {result['tool_call']['tool_name']}")
```

### 方式三：SQL工作流使用

专门为SQL查询和数据分析设计的交互模式：

```bash
python examples/sql_workflow_example.py
```

这个示例专注于SQL相关任务，包括：
- 自然语言到SQL的转换
- SQL查询执行和结果展示
- 数据分析和统计查询
- 完整的SQL工作流演示

## 🎯 Agent特性

### 智能工具选择
- Agent能够根据用户请求智能判断是否需要使用工具
- 支持复杂的多步骤任务执行
- 提供详细的工具执行反馈

### 对话记忆
- 维护完整的对话历史
- 支持上下文理解
- 可导出对话记录

### 执行统计
- 跟踪消息数量、工具调用次数
- 计算工具调用成功率
- 提供详细的工具使用统计

### 安全保护
- 文件操作路径安全检查
- 数学计算表达式安全执行
- 工具调用异常处理

## 🔨 自定义工具

您可以轻松扩展Agent的工具能力：

### 1. 创建自定义工具

```python
from src.tools.base_tools import BaseTool, ToolOutput

class CustomTool(BaseTool):
    def __init__(self):
        super().__init__(
            name="custom_tool",
            description="这是一个自定义工具"
        )
    
    def call(self, param1: str, param2: int = 10) -> ToolOutput:
        try:
            # 实现您的工具逻辑
            result = f"处理参数: {param1}, {param2}"
            return ToolOutput(success=True, result=result)
        except Exception as e:
            return ToolOutput(success=False, result=None, error=str(e))
    
    def get_schema(self):
        return {
            "type": "function",
            "function": {
                "name": self.name,
                "description": self.description,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "param1": {"type": "string", "description": "参数1描述"},
                        "param2": {"type": "integer", "description": "参数2描述", "default": 10}
                    },
                    "required": ["param1"]
                }
            }
        }
```

### 2. 使用自定义工具

```python
from src.agents import ToolCallingAgent
from src.core import LLMFactory

# 创建自定义工具列表
custom_tools = [CustomTool()]

# 创建Agent时指定工具
llm = LLMFactory.create_chat_model(provider="local")
agent = ToolCallingAgent(llm=llm, tools=custom_tools)
```

## 📝 示例对话

```
👤 您: 帮我计算一下 sqrt(144) + sin(0) * 100 的结果

🤖 Agent: 我来帮您计算这个数学表达式。

根据计算结果：sqrt(144) + sin(0) * 100 = 12.0

这个计算过程是：
- sqrt(144) = 12
- sin(0) = 0  
- sin(0) * 100 = 0
- 12 + 0 = 12

所以最终结果是 12.0。

🔧 使用了工具: calculator
✅ 工具执行成功
```

```
👤 您: 搜索一下关于机器学习的信息

🤖 Agent: 我来为您搜索关于机器学习的信息。

根据搜索结果，我找到了以下关于机器学习的信息：

1. **关于'机器学习'的搜索结果 1**
   - 这是关于机器学习的详细信息和说明...

2. **'机器学习'详细介绍** 
   - 深入了解机器学习的相关内容和背景知识...

3. **机器学习官方文档**
   - 官方提供的机器学习文档和使用指南...

这些资源可以帮助您更好地了解机器学习的概念、应用和实践方法。

🔧 使用了工具: web_search
✅ 工具执行成功
```

```
👤 您: 生成一个查询2025年4月电费的SQL语句

🤖 Agent: 我来为您生成查询2025年4月电费的SQL语句。

根据您的需求，生成的SQL语句如下：

```sql
SELECT * FROM electricity_bills 
WHERE year = 2025 AND month = 4
```

这个查询将返回2025年4月的所有电费记录，包括金额、描述等详细信息。

🔧 使用了工具: sql_generator
✅ 工具执行成功
```

```
👤 您: 执行SQL: SELECT * FROM users WHERE status = 'active'

🤖 Agent: 我来执行这个查询活跃用户的SQL语句。

查询执行成功！找到了以下活跃用户：

📊 查询结果: 3 行
📋 详细数据:
  第1行: (1, '张三', '<EMAIL>', '2024-01-15', 'active')
  第2行: (2, '李四', '<EMAIL>', '2024-02-20', 'active')  
  第3行: (4, '赵六', '<EMAIL>', '2025-01-05', 'active')

共找到3个活跃用户，他们的注册时间分布在2024年到2025年之间。

🔧 使用了工具: sql_executor
✅ 工具执行成功
```

## 🔍 故障排除

### 常见问题

1. **Agent初始化失败**
   - 检查`.env`文件配置
   - 确认千问模型服务正在运行
   - 验证API密钥和URL

2. **工具调用失败**
   - 查看工具执行的详细错误信息
   - 检查工具参数格式是否正确
   - 确认文件路径权限（文件工具）

3. **模型响应异常**
   - 检查网络连接
   - 调整温度参数
   - 查看模型服务日志

### 调试建议

- 使用`stats`命令查看执行统计
- 通过`export`命令导出对话记录进行分析
- 检查工具调用的JSON格式是否正确

## 🤝 扩展开发

欢迎贡献更多工具和功能：

- 网络API调用工具
- 数据库操作工具  
- 图像处理工具
- 邮件发送工具
- 定时任务工具

请参考现有工具的实现模式，遵循统一的接口规范。 