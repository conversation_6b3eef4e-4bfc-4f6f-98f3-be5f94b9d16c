# 🔧 API服务管理指南

## 📋 概述

为了解决API服务启动和停止过程中的端口占用问题，我们提供了多种安全的管理方式。

## 🚀 启动方式

### 1. 推荐方式：使用管理脚本

```bash
# 启动服务（安全模式，推荐）
python src/expense_analysis/manage_api.py start

# 启动服务（开发模式）
python src/expense_analysis/manage_api.py start --dev

# 检查服务状态
python src/expense_analysis/manage_api.py status

# 停止服务
python src/expense_analysis/manage_api.py stop

# 重启服务
python src/expense_analysis/manage_api.py restart
```

### 2. 直接启动

```bash
# 安全模式启动（推荐）
python src/expense_analysis/start_api_safe.py

# 开发模式启动
python src/expense_analysis/start_api.py

# 生产模式启动
python src/expense_analysis/start_api_prod.py
```

## 🛑 停止方式

### ✅ 正确的停止方式

1. **使用Ctrl+C**（推荐）
   ```bash
   # 在运行API的终端中按 Ctrl+C
   ```

2. **使用管理脚本**
   ```bash
   python src/expense_analysis/manage_api.py stop
   ```

3. **使用简化停止脚本**（无需psutil）
   ```bash
   python src/expense_analysis/stop_api_simple.py
   ```

4. **使用完整停止脚本**（需要psutil）
   ```bash
   python src/expense_analysis/stop_api.py
   ```

5. **强制停止**（最后手段）
   ```bash
   python src/expense_analysis/force_stop_api.py
   ```

### ❌ 避免的停止方式

```bash
# 不要使用这些命令！
kill -9 <pid>           # 强制终止，会留下子进程
pkill -f start_api      # 可能误杀其他进程
```

## 🔍 故障排除

### 端口被占用问题

如果遇到"端口8002已被占用"的错误：

```bash
# 1. 使用停止脚本清理
python src/expense_analysis/stop_api.py

# 2. 检查状态
python src/expense_analysis/manage_api.py status

# 3. 手动查找占用进程
netstat -tulpn | grep 8002    # Linux
lsof -i :8002                 # macOS
netstat -ano | findstr 8002   # Windows
```

### 进程清理

如果仍有残留进程：

```bash
# 查找相关进程
ps aux | grep expense_analysis
ps aux | grep uvicorn

# 优雅终止
kill <pid>

# 强制终止（最后手段）
kill -9 <pid>
```

## 📊 服务状态检查

```bash
# 检查API服务状态
python src/expense_analysis/manage_api.py status

# 测试API是否响应
curl http://localhost:8002/health

# 检查端口占用
netstat -tulpn | grep 8002
```

## 🔧 不同启动模式对比

| 模式 | 文件 | 特点 | 适用场景 |
|------|------|------|----------|
| 安全模式 | `start_api_safe.py` | 优雅关闭、进程管理、端口检查 | **推荐使用** |
| 开发模式 | `start_api.py` | 文件监控、自动重载 | 开发调试 |
| 生产模式 | `start_api_prod.py` | 无文件监控、稳定运行 | 生产环境 |

## 💡 最佳实践

1. **日常开发**：使用安全模式 `manage_api.py start`
2. **调试代码**：使用开发模式 `manage_api.py start --dev`
3. **生产部署**：使用生产模式 `start_api_prod.py`
4. **停止服务**：始终使用 `Ctrl+C` 或停止脚本
5. **遇到问题**：先运行 `stop_api.py` 清理环境

## 🚨 注意事项

- ⚠️ 避免使用 `kill -9` 强制终止
- ⚠️ 确保在正确的目录下运行脚本
- ⚠️ 如果端口被其他程序占用，需要手动处理
- ✅ 使用管理脚本可以自动处理大部分问题
- ✅ 安全模式会自动清理残留进程

## 🆘 常见问题

**Q: 为什么会有端口占用问题？**
A: uvicorn会创建多个进程（主进程、reloader进程、worker进程），`kill -9`只杀死主进程。

**Q: 如何确保完全停止？**
A: 使用我们提供的停止脚本，它会清理所有相关进程。

**Q: 开发时应该用哪种模式？**
A: 推荐使用安全模式，既有文件监控又能优雅关闭。

**Q: 生产环境怎么部署？**
A: 使用生产模式，配合进程管理工具如systemd、supervisor等。
