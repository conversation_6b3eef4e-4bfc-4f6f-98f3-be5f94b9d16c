#!/usr/bin/env python3
"""
费用分析API服务启动脚本 - 生产环境版本
不使用reload模式，避免文件监控问题
"""
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

def main():
    """启动API服务 - 生产模式"""
    print("💰 启动费用分析API服务 (生产模式)")
    print("=" * 40)
    print("📍 端口: 8002")
    print("📖 文档: http://localhost:8002/docs")
    print("🔧 模式: 生产模式 (无文件监控)")
    print("=" * 40)
    
    try:
        import uvicorn
        import logging
        
        # 配置日志级别
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 禁用不必要的日志
        logging.getLogger("watchfiles").setLevel(logging.ERROR)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.error").setLevel(logging.INFO)

        print("🚀 正在启动服务...")
        uvicorn.run(
            "src.expense_analysis.api.export_api:app",
            host="0.0.0.0",
            port=8002,
            reload=False,  # 关闭reload模式
            log_level="info"
        )
        
    except ImportError:
        print("❌ 错误: 未安装uvicorn")
        print("请运行: pip install uvicorn")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
