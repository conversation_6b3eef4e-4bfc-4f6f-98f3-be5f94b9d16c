"""
费用分类主程序
用于执行费用备注的AI分类任务
"""
import argparse
import json
import os
import sys
import pymysql
from datetime import datetime
from typing import Dict, List, Optional, Any

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.expense_analysis.agents import ExpenseClassificationAgent
from src.expense_analysis.services.data_service import DataService
from src.expense_analysis.models import SamplingConfig

class ExpenseClassifier:
    """费用分类器主类"""
    
    def __init__(self, llm_provider: str = "local"):
        """
        初始化分类器
        
        Args:
            llm_provider: LLM提供商
        """
        self.agent = ExpenseClassificationAgent(llm_provider)
        self.data_service = DataService()
        # 将分类文件放在expense_analysis目录下，避免污染项目根目录
        self.categories_file = os.path.join(os.path.dirname(__file__), "discovered_categories.json")
    
    def discover_and_save_categories(self, table_name: str, filters: Dict = None, 
                                   config: SamplingConfig = None) -> List[str]:
        """
        发现并保存分类类别
        
        Args:
            table_name: 表名
            filters: 过滤条件
            config: 采样配置
            
        Returns:
            发现的分类列表
        """
        print("=" * 50)
        print("开始分类发现阶段")
        print("=" * 50)
        
        categories = self.agent.discover_categories_from_sample(table_name, filters, config)

        if categories:
            # 手动添加"无备注"类别（用于处理空备注数据）
            if "无备注" not in categories:
                categories.append("无备注")
                print("✅ 已自动添加'无备注'类别用于处理空备注数据")

            # 保存分类到文件
            self._save_categories(categories, table_name)
            print(f"\n发现的分类已保存到: {self.categories_file}")

            # 显示发现的分类
            print("\n发现的费用分类类别:")
            for i, category in enumerate(categories, 1):
                print(f"{i}. {category}")

            return categories
        else:
            print("未能发现有效的分类类别")
            return []
    
    def load_categories(self, table_name: str = None) -> Optional[List[str]]:
        """
        从文件加载分类类别
        
        Args:
            table_name: 表名（可选，用于验证）
            
        Returns:
            分类列表或None
        """
        if not os.path.exists(self.categories_file):
            return None
        
        try:
            with open(self.categories_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if table_name and data.get('table_name') != table_name:
                print(f"警告: 分类文件中的表名({data.get('table_name')})与当前表名({table_name})不匹配")
            
            return data.get('categories', [])
        except Exception as e:
            print(f"加载分类文件失败: {e}")
            return None
    
    def classify_all_data(self, table_name: str, categories: List[str], 
                         filters: Dict = None, batch_size: int = 50) -> bool:
        """
        对所有数据进行分类
        
        Args:
            table_name: 表名
            categories: 分类类别列表
            filters: 过滤条件
            batch_size: 批处理大小
            
        Returns:
            是否成功
        """
        print("=" * 50)
        print("开始全量数据分类阶段")
        print("=" * 50)
        
        try:
            # 1. 获取总记录数
            total_count = self.data_service.get_table_count(table_name, filters)
            print(f"表 {table_name} 共有 {total_count:,} 条记录需要分类")
            
            if total_count == 0:
                print("没有数据需要分类")
                return True
            
            # 2. 分批处理
            processed = 0
            batch_num = 0
            
            while processed < total_count:
                batch_num += 1
                print(f"\n处理第 {batch_num} 批数据...")
                
                # 获取一批数据
                batch_records = self._get_batch_records(table_name, filters, batch_size, processed)
                
                if not batch_records:
                    break
                
                # 分离有备注和无备注的记录
                records_with_remarks = []
                records_without_remarks = []

                for record in batch_records:
                    has_content = False

                    # 检查是否有备注内容（支持JSON格式和直接字符串格式）
                    if record.paymentdetail_note and record.paymentdetail_note.strip():
                        try:
                            # 尝试解析JSON格式
                            from src.expense_analysis.models import RemarkDetail
                            remark_details = RemarkDetail.from_json(record.paymentdetail_note)
                            if any(detail.content and detail.content.strip() for detail in remark_details):
                                has_content = True
                        except:
                            # 如果不是JSON格式，直接作为字符串处理
                            has_content = True

                    if record.mandatory_note and record.mandatory_note.strip():
                        has_content = True

                    if has_content:
                        records_with_remarks.append(record)
                    else:
                        records_without_remarks.append(record)

                # 处理有备注的记录
                classification_results = {}
                if records_with_remarks:
                    comprehensive_remarks = self.data_service.get_comprehensive_remarks_for_classification(records_with_remarks)
                    if comprehensive_remarks:
                        # 传递可变的分类列表，支持动态扩展
                        mutable_categories = categories.copy()
                        classification_results = self.agent.classify_comprehensive_remarks(comprehensive_remarks, mutable_categories, 10)

                        # 如果分类列表有变化，更新并保存
                        if len(mutable_categories) > len(categories):
                            new_categories = mutable_categories[len(categories):]
                            categories.extend(new_categories)
                            print(f"🔄 更新分类列表，新增: {', '.join(new_categories)}")
                            # 保存更新后的分类
                            self._save_categories(categories, "updated_categories")

                # 处理无备注的记录
                for record in records_without_remarks:
                    from src.expense_analysis.models import ClassificationResult
                    classification_results[record.billaccountpaymentdetail_id] = ClassificationResult(
                        category="无备注",
                        confidence=1.0,
                        reason="该记录无备注内容"
                    )

                # 保存所有分类结果
                if classification_results:
                    self._save_comprehensive_classification_results(table_name, batch_records, classification_results)
                
                processed += len(batch_records)
                print(f"已处理 {processed:,}/{total_count:,} 条记录 ({processed/total_count*100:.1f}%)")
            
            print(f"\n✅ 全量分类完成！共处理 {processed:,} 条记录")
            return True
            
        except Exception as e:
            print(f"❌ 全量分类失败: {e}")
            return False
    
    def _save_categories(self, categories: List[str], table_name: str):
        """保存分类到文件"""
        data = {
            'table_name': table_name,
            'categories': categories,
            'created_time': datetime.now().isoformat(),
            'total_categories': len(categories)
        }
        
        with open(self.categories_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def _get_batch_records(self, table_name: str, filters: Dict, batch_size: int, offset: int):
        """获取一批记录"""
        with self.data_service.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)
            
            sql = """
            SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                   reg_id, reg_name, billamount_date, paymentdetail_note,
                   mandatory_note, search_keywords, auditing_state,
                   last_review_result, last_review_comment, billamount_startdate, billamount_enddate
            FROM {}
            WHERE 1=1
            """.format(table_name)
            
            params = []
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            # 处理rpt_month筛选，参考您的逻辑：指定年月在开始和结束日期之间
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            params.append(year_month)
                        else:
                            sql += f" AND {key} = %s"
                            params.append(value)
            
            sql += f" LIMIT {batch_size} OFFSET {offset}"
            
            cursor.execute(sql, params)
            results = cursor.fetchall()
            
            # 传递目标月份
            target_month = filters.get('rpt_month') if filters else None
            return [self.data_service._dict_to_expense_record(row, target_month) for row in results]
    
    def show_classification_summary(self, filters: Dict = None):
        """显示分类汇总结果"""
        try:
            stats = self.data_service.get_classification_statistics(filters)

            if not stats:
                print("暂无分类结果")
                return

            print("\n📊 分类结果汇总:")
            print("-" * 50)
            total = sum(stats.values())

            for category, count in stats.items():
                percentage = count / total * 100
                print(f"{category:<20} {count:>8,} 条 ({percentage:>5.1f}%)")

            print("-" * 50)
            print(f"{'总计':<20} {total:>8,} 条 (100.0%)")

        except Exception as e:
            print(f"获取分类汇总失败: {e}")

    def _save_comprehensive_classification_results(self, table_name: str, records: List,
                                                 classification_results: Dict[str, Any]):
        """保存综合分析的分类结果"""
        if not classification_results:
            return

        # 确保分类结果表存在
        self.data_service._ensure_classification_table()

        with self.data_service.get_connection() as conn:
            cursor = conn.cursor()

            # 准备插入数据
            insert_data = []
            current_time = datetime.now()

            for record in records:
                record_id = record.billaccountpaymentdetail_id

                # 获取分类结果
                if record_id in classification_results:
                    result = classification_results[record_id]
                    category = result.category
                    confidence = result.confidence
                else:
                    category = "其他费用"
                    confidence = 0.1

                # 收集原始备注
                original_remarks = []

                if record.paymentdetail_note:
                    from src.expense_analysis.models import RemarkDetail
                    remark_details = RemarkDetail.from_json(record.paymentdetail_note)
                    for detail in remark_details:
                        if detail.content and detail.content.strip():
                            remark_text = f"[{detail.dictgroup_name or ''}>{detail.dict_name or ''}] {detail.content.strip()}"
                            original_remarks.append(remark_text)

                if record.mandatory_note and record.mandatory_note.strip():
                    original_remarks.append(f"[必填备注] {record.mandatory_note.strip()}")

                # 准备插入数据
                insert_data.append((
                    record.billaccountpaymentdetail_id,
                    record.payment_code,
                    record.preg_id,
                    record.preg_name,
                    record.reg_id,
                    record.reg_name,
                    record.rpt_month,
                    '; '.join(original_remarks),  # 格式化的备注
                    category,
                    confidence,
                    current_time,
                    record.billamount_startdate,
                    record.billamount_enddate
                ))

            # 批量插入或更新
            sql = """
            INSERT INTO ele_payment_ai_sort
            (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
             reg_id, reg_name, rpt_month, original_remark, ai_category,
             ai_confidence, ai_classified_time, billamount_startdate, billamount_enddate)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            ai_category = VALUES(ai_category),
            ai_confidence = VALUES(ai_confidence),
            ai_classified_time = VALUES(ai_classified_time),
            original_remark = VALUES(original_remark),
            billamount_startdate = VALUES(billamount_startdate),
            billamount_enddate = VALUES(billamount_enddate)
            """

            cursor.executemany(sql, insert_data)
            conn.commit()

            print(f"✅ 已保存 {len(insert_data)} 条综合分析结果到 ele_payment_ai_sort 表")

    def clear_classification_data(self, filters: Dict = None):
        """清空分类结果表数据"""
        try:
            with self.data_service.get_connection() as conn:
                cursor = conn.cursor()

                if filters:
                    # 按条件删除
                    sql = "DELETE FROM ele_payment_ai_sort WHERE 1=1"
                    params = []

                    for key, value in filters.items():
                        if value is not None:
                            if key == 'rpt_month':
                                sql += " AND rpt_month = %s"
                                params.append(value)
                            elif key == 'preg_id':
                                sql += " AND preg_id = %s"
                                params.append(value)

                    cursor.execute(sql, params)
                    deleted_count = cursor.rowcount
                    print(f"🗑️  已删除 {deleted_count:,} 条符合条件的分类数据")
                else:
                    # 清空全表
                    cursor.execute("TRUNCATE TABLE ele_payment_ai_sort")
                    print("🗑️  已清空分类结果表 ele_payment_ai_sort")

                conn.commit()

        except Exception as e:
            print(f"❌ 清理数据失败: {e}")

    @staticmethod
    def generate_month_range(start_month: str, end_month: str) -> List[str]:
        """生成月份范围列表"""
        if len(start_month) != 6 or len(end_month) != 6:
            raise ValueError("月份格式应为YYYYMM")

        start_year = int(start_month[:4])
        start_mon = int(start_month[4:6])
        end_year = int(end_month[:4])
        end_mon = int(end_month[4:6])

        months = []
        current_year = start_year
        current_month = start_mon

        while (current_year < end_year) or (current_year == end_year and current_month <= end_mon):
            months.append(f"{current_year:04d}{current_month:02d}")

            current_month += 1
            if current_month > 12:
                current_month = 1
                current_year += 1

        return months

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='费用备注AI分类工具',
        epilog="""
使用示例:
  # 发现分类（1000条样本）
  python expense_classifier.py --table clean_ele_payment --mode discover --sample-size 1000

  # 处理单个月份
  python expense_classifier.py --table clean_ele_payment --mode full --month 202504 --clear-data

  # 处理月份范围（推荐）
  python expense_classifier.py --table clean_ele_payment --mode full --start-month 202501 --end-month 202505 --clear-data

  # 处理指定地市的月份范围
  python expense_classifier.py --table clean_ele_payment --mode full --start-month 202501 --end-month 202505 --city 440100 --clear-data
        """,
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    parser.add_argument('--table', required=True, help='表名')
    parser.add_argument('--mode', choices=['discover', 'classify', 'full'], 
                       default='full', help='运行模式')
    parser.add_argument('--llm', choices=['local', 'openai', 'anthropic'], 
                       default='local', help='LLM提供商')
    parser.add_argument('--sample-size', type=int, default=1000, help='采样大小')
    parser.add_argument('--batch-size', type=int, default=50, help='批处理大小')
    parser.add_argument('--month', help='筛选月份 (YYYYMM格式，如202504)')
    parser.add_argument('--start-month', help='开始月份 (YYYYMM格式，如202501)')
    parser.add_argument('--end-month', help='结束月份 (YYYYMM格式，如202505)')
    parser.add_argument('--city', help='筛选地市ID')
    parser.add_argument('--clear-data', action='store_true', help='执行前清空分类结果表')

    args = parser.parse_args()

    # 处理月份参数
    months_to_process = []
    base_filters = {}

    if args.start_month and args.end_month:
        # 月份范围模式
        try:
            months_to_process = ExpenseClassifier.generate_month_range(args.start_month, args.end_month)
            print(f"📅 将处理月份范围: {args.start_month} 到 {args.end_month} (共{len(months_to_process)}个月)")
            print(f"月份列表: {', '.join(months_to_process)}")
        except ValueError as e:
            print(f"错误：{e}")
            return
    elif args.month:
        # 单月模式
        if len(args.month) == 6 and args.month.isdigit():
            months_to_process = [args.month]
            print(f"📅 将处理单个月份: {args.month}")
        else:
            print("错误：月份格式应为YYYYMM，如202504")
            return
    else:
        # 全量模式
        months_to_process = [None]
        print("📅 将处理全量数据（不限月份）")

    if args.city:
        base_filters['preg_id'] = args.city
        print(f"🏙️  限制地市: {args.city}")
    
    # 创建分类器
    classifier = ExpenseClassifier(args.llm)
    config = SamplingConfig(sample_size=args.sample_size)

    # 数据清理
    if args.clear_data:
        print("\n🗑️  清理现有分类数据...")
        if months_to_process[0] is not None:
            # 按月份清理
            for month in months_to_process:
                clear_filters = base_filters.copy()
                clear_filters['rpt_month'] = month
                classifier.clear_classification_data(clear_filters)
        else:
            # 清理全部
            classifier.clear_classification_data(base_filters if base_filters else None)

    # 发现分类阶段
    if args.mode in ['discover', 'full']:
        print("\n" + "="*60)
        print("🔍 分类发现阶段")
        print("="*60)

        # 使用第一个月份或全量数据进行分类发现
        discover_filters = base_filters.copy()
        if months_to_process[0] is not None:
            discover_filters['rpt_month'] = months_to_process[0]
            print(f"使用 {months_to_process[0]} 月份数据进行分类发现")

        categories = classifier.discover_and_save_categories(args.table, discover_filters, config)
        if not categories:
            print("分类发现失败，程序退出")
            return
    else:
        # 加载已有分类
        categories = classifier.load_categories(args.table)
        if not categories:
            print("未找到已保存的分类，请先运行发现模式")
            return

    # 分类执行阶段
    if args.mode in ['classify', 'full']:
        print("\n" + "="*60)
        print("🏷️  批量分类阶段")
        print("="*60)

        total_success = 0
        total_months = len(months_to_process)

        for i, month in enumerate(months_to_process, 1):
            current_filters = base_filters.copy()
            if month is not None:
                current_filters['rpt_month'] = month
                print(f"\n📅 处理月份 {month} ({i}/{total_months})")
            else:
                print(f"\n📅 处理全量数据")

            # 执行分类
            success = classifier.classify_all_data(args.table, categories, current_filters, args.batch_size)
            if success:
                total_success += 1
                print(f"✅ 月份 {month or '全量'} 分类完成")
            else:
                print(f"❌ 月份 {month or '全量'} 分类失败")

        # 总结
        print(f"\n{'='*60}")
        print(f"🎉 分类任务完成！成功处理 {total_success}/{total_months} 个月份")
        print(f"{'='*60}")

        # 显示最终汇总（所有月份）
        if total_success > 0:
            print("\n📊 最终分类汇总:")
            classifier.show_classification_summary(base_filters if base_filters else None)

if __name__ == '__main__':
    main()
