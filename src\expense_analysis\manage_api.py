#!/usr/bin/env python3
"""
费用分析API服务管理脚本
统一管理API服务的启动、停止、重启和状态检查
"""
import os
import sys
import subprocess
import argparse
from pathlib import Path

# 尝试导入psutil，如果没有则使用简化版本
try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False
    print("⚠️ 未安装psutil，功能受限。建议运行: pip install psutil")

def check_api_status():
    """检查API服务状态"""
    print("🔍 检查API服务状态...")

    if not HAS_PSUTIL:
        # 简化版本：通过HTTP请求检查
        try:
            import requests
            response = requests.get("http://localhost:8002/health", timeout=3)
            if response.status_code == 200:
                print("✅ API服务正在运行 (HTTP检查)")
                return True
            else:
                print("❌ API服务响应异常")
                return False
        except:
            print("❌ API服务未运行 (无法连接)")
            return False

    # 完整版本：使用psutil检查端口
    port_occupied = False
    occupying_processes = []

    try:
        for conn in psutil.net_connections(kind='inet'):
            if conn.laddr.port == 8002 and conn.status == psutil.CONN_LISTEN:
                port_occupied = True
                try:
                    process = psutil.Process(conn.pid)
                    occupying_processes.append(process)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
    except Exception as e:
        print(f"⚠️ 检查端口时出错: {e}")

    if port_occupied:
        print("✅ API服务正在运行")
        print("📋 占用8002端口的进程:")
        for proc in occupying_processes:
            try:
                cmdline = ' '.join(proc.cmdline()[:3])
                print(f"  - PID {proc.pid}: {proc.name()} ({cmdline}...)")
            except:
                print(f"  - PID {proc.pid}: {proc.name()}")
        return True
    else:
        print("❌ API服务未运行")
        return False

def start_api(safe_mode=True):
    """启动API服务"""
    print("🚀 启动API服务...")
    
    # 检查是否已经运行
    if check_api_status():
        print("⚠️ API服务已在运行，请先停止")
        return False
    
    try:
        script_name = "start_api_safe.py" if safe_mode else "start_api.py"
        script_path = Path(__file__).parent / script_name
        
        if not script_path.exists():
            print(f"❌ 启动脚本不存在: {script_path}")
            return False
        
        print(f"📝 使用脚本: {script_name}")
        print("💡 使用 Ctrl+C 停止服务")
        
        # 启动服务
        subprocess.run([sys.executable, str(script_path)])
        return True
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def stop_api():
    """停止API服务"""
    print("🛑 停止API服务...")
    
    try:
        script_path = Path(__file__).parent / "stop_api.py"
        
        if not script_path.exists():
            print(f"❌ 停止脚本不存在: {script_path}")
            return False
        
        # 运行停止脚本
        result = subprocess.run([sys.executable, str(script_path)])
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 停止失败: {e}")
        return False

def restart_api(safe_mode=True):
    """重启API服务"""
    print("🔄 重启API服务...")
    
    # 先停止
    if not stop_api():
        print("❌ 停止服务失败")
        return False
    
    # 等待一下
    import time
    print("⏳ 等待服务完全停止...")
    time.sleep(3)
    
    # 再启动
    return start_api(safe_mode)

def show_logs():
    """显示日志（如果有的话）"""
    print("📋 查看API日志...")
    
    log_files = [
        "api.log",
        "uvicorn.log",
        "expense_analysis.log"
    ]
    
    found_logs = False
    for log_file in log_files:
        log_path = Path(log_file)
        if log_path.exists():
            found_logs = True
            print(f"\n📄 {log_file}:")
            try:
                with open(log_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 显示最后20行
                    for line in lines[-20:]:
                        print(f"  {line.rstrip()}")
            except Exception as e:
                print(f"  ❌ 读取日志失败: {e}")
    
    if not found_logs:
        print("ℹ️ 未找到日志文件")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="费用分析API服务管理工具")
    parser.add_argument("action", choices=["start", "stop", "restart", "status", "logs"],
                       help="操作类型")
    parser.add_argument("--dev", action="store_true", 
                       help="使用开发模式启动（默认使用安全模式）")
    
    args = parser.parse_args()
    
    print("🔧 费用分析API服务管理工具")
    print("=" * 40)
    
    if args.action == "status":
        check_api_status()
    elif args.action == "start":
        safe_mode = not args.dev
        start_api(safe_mode)
    elif args.action == "stop":
        stop_api()
    elif args.action == "restart":
        safe_mode = not args.dev
        restart_api(safe_mode)
    elif args.action == "logs":
        show_logs()

if __name__ == "__main__":
    if len(sys.argv) == 1:
        print("🔧 费用分析API服务管理工具")
        print("=" * 40)
        print("使用方法:")
        print("  python manage_api.py start     # 启动服务（安全模式）")
        print("  python manage_api.py start --dev  # 启动服务（开发模式）")
        print("  python manage_api.py stop      # 停止服务")
        print("  python manage_api.py restart   # 重启服务")
        print("  python manage_api.py status    # 检查状态")
        print("  python manage_api.py logs      # 查看日志")
        print("\n💡 推荐使用安全模式启动，支持优雅关闭")
    else:
        main()
