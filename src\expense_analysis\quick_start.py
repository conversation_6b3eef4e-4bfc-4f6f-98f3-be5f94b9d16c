#!/usr/bin/env python3
"""
费用分类快速启动脚本
"""
import os
import sys

def main():
    """主函数"""
    print("=" * 60)
    print("费用分类AI工具 - 快速启动")
    print("=" * 60)
    
    print("\n推荐的使用流程:")
    print("1. 发现分类（1000条样本）")
    print("2. 处理月份范围（自动清理数据）")
    print("3. 查看分类结果")
    
    # 获取用户输入
    print("\n请选择操作:")
    print("1. 发现分类类别（推荐1000条样本）")
    print("2. 处理单个月份")
    print("3. 处理月份范围（推荐）")
    print("4. 查看帮助")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        # 发现分类
        sample_size = input("采样大小 (默认1000): ").strip() or "1000"
        city = input("地市ID (留空表示全部): ").strip()
        
        cmd = f"python expense_classifier.py --table clean_ele_payment --mode discover --sample-size {sample_size}"
        if city:
            cmd += f" --city {city}"
        
        print(f"\n执行命令: {cmd}")
        os.system(cmd)
        
    elif choice == "2":
        # 单个月份
        month = input("请输入月份 (YYYYMM格式，如202504): ").strip()
        if not month or len(month) != 6 or not month.isdigit():
            print("错误：月份格式不正确")
            return
        
        city = input("地市ID (留空表示全部): ").strip()
        clear = input("是否清理现有数据? (y/N): ").strip().lower()
        
        cmd = f"python expense_classifier.py --table clean_ele_payment --mode full --month {month}"
        if city:
            cmd += f" --city {city}"
        if clear == 'y':
            cmd += " --clear-data"
        
        print(f"\n执行命令: {cmd}")
        os.system(cmd)
        
    elif choice == "3":
        # 月份范围
        start_month = input("开始月份 (YYYYMM格式，如202501): ").strip()
        end_month = input("结束月份 (YYYYMM格式，如202505): ").strip()
        
        if not start_month or len(start_month) != 6 or not start_month.isdigit():
            print("错误：开始月份格式不正确")
            return
        if not end_month or len(end_month) != 6 or not end_month.isdigit():
            print("错误：结束月份格式不正确")
            return
        
        city = input("地市ID (留空表示全部): ").strip()
        clear = input("是否清理现有数据? (Y/n): ").strip().lower()
        
        cmd = f"python expense_classifier.py --table clean_ele_payment --mode full --start-month {start_month} --end-month {end_month}"
        if city:
            cmd += f" --city {city}"
        if clear != 'n':
            cmd += " --clear-data"
        
        print(f"\n执行命令: {cmd}")
        print(f"将处理 {start_month} 到 {end_month} 的数据")
        
        confirm = input("确认执行? (Y/n): ").strip().lower()
        if confirm != 'n':
            os.system(cmd)
        
    elif choice == "4":
        # 显示帮助
        os.system("python expense_classifier.py --help")
        
    else:
        print("无效选择")

if __name__ == '__main__':
    main()
