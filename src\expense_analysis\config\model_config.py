"""
费用分析专用模型配置
简化配置，只使用一个模型
"""

# 费用分析专用LLM配置
EXPENSE_ANALYSIS_LLM_CONFIG = {
    # 固定使用Qwen3-235B模型
    "model_name": "Qwen3-235B-A22B",

    # 模型服务配置
    "api_url": "http://ai.ai.iot.chinamobile.com/imaas/v1/chat/completions",
    "api_key": "sk-bCzKwiWgIHHKhTDxxi8GcVT46XKcXz7aqqaP2aXOVLYjhbZZ",

    # 分类任务优化参数
    "temperature": 0.1,      # 低温度，提高一致性
    "max_tokens": 2000,      # 适中的token数量
    "timeout": 300,          # 5分钟超时

    # 性能优化
    "preserve_think_tags": False,   # 禁用思考标签
    "stream": False,               # 禁用流式输出
}

def get_model_config():
    """
    获取模型配置

    Returns:
        dict: 模型配置
    """
    return EXPENSE_ANALYSIS_LLM_CONFIG.copy()

if __name__ == "__main__":
    config = get_model_config()
    print("🤖 费用分析模型配置")
    print("=" * 40)
    print(f"📝 模型: {config['model_name']}")
    print(f"🌡️ 温度: {config['temperature']}")
    print(f"⏱️ 超时: {config['timeout']}秒")
