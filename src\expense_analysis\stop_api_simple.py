#!/usr/bin/env python3
"""
费用分析API服务简单停止脚本
不依赖psutil，使用系统命令
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(cmd):
    """运行系统命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def find_and_kill_processes():
    """查找并终止相关进程"""
    system = platform.system().lower()
    
    if system == "windows":
        return kill_windows_processes()
    else:
        return kill_unix_processes()

def kill_windows_processes():
    """Windows系统下终止进程"""
    print("🔍 在Windows系统中查找进程...")
    
    # 查找占用8002端口的进程
    success, output, error = run_command("netstat -ano | findstr :8002")
    
    if success and output:
        print("📋 找到占用8002端口的连接:")
        lines = output.strip().split('\n')
        pids = set()
        
        for line in lines:
            parts = line.split()
            if len(parts) >= 5 and ':8002' in parts[1]:
                pid = parts[-1]
                pids.add(pid)
                print(f"  - 连接: {parts[1]} -> PID: {pid}")
        
        # 终止进程
        killed_count = 0
        for pid in pids:
            print(f"🔄 终止进程 PID: {pid}")
            success, _, _ = run_command(f"taskkill /F /PID {pid}")
            if success:
                print(f"✅ 成功终止进程 {pid}")
                killed_count += 1
            else:
                print(f"❌ 无法终止进程 {pid}")
        
        return killed_count > 0
    else:
        print("✅ 未找到占用8002端口的进程")
        return True

def kill_unix_processes():
    """Unix/Linux/macOS系统下终止进程"""
    print("🔍 在Unix系统中查找进程...")
    
    # 查找占用8002端口的进程
    success, output, error = run_command("lsof -ti:8002")
    
    if success and output:
        pids = output.strip().split('\n')
        print(f"📋 找到 {len(pids)} 个占用8002端口的进程:")
        
        killed_count = 0
        for pid in pids:
            if pid.strip():
                print(f"🔄 终止进程 PID: {pid}")
                # 先尝试优雅终止
                success, _, _ = run_command(f"kill {pid}")
                if success:
                    print(f"✅ 成功终止进程 {pid}")
                    killed_count += 1
                else:
                    # 强制终止
                    print(f"⚡ 强制终止进程 {pid}")
                    success, _, _ = run_command(f"kill -9 {pid}")
                    if success:
                        killed_count += 1
        
        return killed_count > 0
    else:
        # 尝试使用netstat
        success, output, error = run_command("netstat -tulpn 2>/dev/null | grep :8002")
        if success and output:
            print("📋 通过netstat找到进程:")
            lines = output.strip().split('\n')
            pids = set()
            
            for line in lines:
                parts = line.split()
                for part in parts:
                    if '/' in part and part.split('/')[0].isdigit():
                        pid = part.split('/')[0]
                        pids.add(pid)
            
            killed_count = 0
            for pid in pids:
                print(f"🔄 终止进程 PID: {pid}")
                success, _, _ = run_command(f"kill {pid}")
                if success:
                    killed_count += 1
            
            return killed_count > 0
        else:
            print("✅ 未找到占用8002端口的进程")
            return True

def cleanup_pid_file():
    """清理PID文件"""
    pid_file = Path("api_server.pid")
    if pid_file.exists():
        try:
            pid_file.unlink()
            print("✅ PID文件已清理")
        except Exception as e:
            print(f"⚠️ 清理PID文件失败: {e}")

def check_port_status():
    """检查端口状态"""
    system = platform.system().lower()
    
    if system == "windows":
        success, output, _ = run_command("netstat -ano | findstr :8002")
    else:
        success, output, _ = run_command("lsof -i:8002")
    
    if success and output.strip():
        print("⚠️ 8002端口仍被占用:")
        print(output)
        return False
    else:
        print("✅ 8002端口已释放")
        return True

def main():
    """主函数"""
    print("🛑 费用分析API服务停止脚本 (简化版)")
    print("=" * 45)
    
    # 1. 终止相关进程
    print("🔄 正在终止API相关进程...")
    if find_and_kill_processes():
        print("✅ 进程终止完成")
    else:
        print("⚠️ 进程终止可能不完整")
    
    # 2. 清理PID文件
    print("\n🧹 清理临时文件...")
    cleanup_pid_file()
    
    # 3. 等待一下再检查
    print("\n⏳ 等待进程完全退出...")
    import time
    time.sleep(2)
    
    # 4. 最终检查
    print("🔍 最终检查端口状态...")
    if check_port_status():
        print("\n✅ API服务停止成功")
    else:
        print("\n⚠️ 可能仍有进程占用端口，请手动检查")
        print("💡 可以尝试重启系统或手动终止相关进程")

if __name__ == "__main__":
    main()
