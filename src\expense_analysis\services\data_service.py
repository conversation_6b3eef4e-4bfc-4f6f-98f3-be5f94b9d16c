"""
数据服务模块
负责数据库操作和数据处理
"""
import pymysql
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import json
import random
import math
from scipy import stats

from ..config import db_config
from ..models import ExpenseRecord, RemarkDetail, SamplingConfig

class DataService:
    """数据服务类"""

    def __init__(self, database_name: str = None):
        self.db_config = db_config
        self.database_name = database_name

    def get_connection(self):
        """获取数据库连接"""
        config = self.db_config.get_pymysql_config()
        # 如果指定了数据库名称，则覆盖默认配置
        if self.database_name:
            config['database'] = self.database_name
        return pymysql.connect(**config)
    
    def get_table_count(self, table_name: str, filters: Dict[str, Any] = None) -> int:
        """获取表记录总数"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            sql = f"SELECT COUNT(*) FROM {table_name}"
            params = []
            
            if filters:
                conditions = []
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            # 处理rpt_month筛选，参考您的逻辑：指定年月在开始和结束日期之间
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            conditions.append("%s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')")
                            params.append(year_month)
                        else:
                            conditions.append(f"{key} = %s")
                            params.append(value)

                if conditions:
                    sql += " WHERE " + " AND ".join(conditions)
            
            cursor.execute(sql, params)
            result = cursor.fetchone()
            return result[0] if result else 0
    
    def calculate_sample_size(self, population_size: int, config: SamplingConfig) -> int:
        """计算统计学有效样本量"""
        if population_size <= 0:
            return 0
        
        # 使用统计学公式计算样本量
        z_score = stats.norm.ppf((1 + config.confidence_level) / 2)
        sample_size = (z_score**2 * 0.25) / (config.margin_error**2)
        
        # 有限总体修正
        if population_size < float('inf'):
            sample_size = sample_size / (1 + (sample_size - 1) / population_size)
        
        # 确保不超过总体大小和配置的最大样本量
        return min(int(math.ceil(sample_size)), population_size, config.sample_size)
    
    def get_stratified_sample(self, table_name: str, filters: Dict[str, Any],
                            config: SamplingConfig = None, limit: int = None) -> List[str]:
        """确保采样到足够的有备注数据"""
        # 如果没有传入config，使用limit创建默认config
        if config is None:
            from src.expense_analysis.models import SamplingConfig
            config = SamplingConfig(sample_size=limit or 1000)

        # 如果传入了limit，覆盖config中的sample_size
        if limit is not None:
            config.sample_size = limit

        print(f"开始采样 {config.sample_size} 条有备注的数据...")

        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            # 先检查有多少条有有效备注的数据（排除无效内容）
            count_sql = """
            SELECT COUNT(*) as total
            FROM {}
            WHERE (
                (paymentdetail_note IS NOT NULL AND paymentdetail_note != '' AND paymentdetail_note != '[]'
                 AND paymentdetail_note != 'null' AND paymentdetail_note != '无' AND paymentdetail_note != '退回'
                 AND paymentdetail_note != '0' AND paymentdetail_note != '1' AND LENGTH(paymentdetail_note) > 3)
                OR (mandatory_note IS NOT NULL AND mandatory_note != '' AND mandatory_note != 'null'
                    AND mandatory_note != '无' AND mandatory_note != '退回' AND LENGTH(mandatory_note) > 3)
            )
            """.format(table_name)

            count_params = []

            # 添加过滤条件
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            count_sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            count_params.append(year_month)
                        elif key == 'preg_id':
                            count_sql += " AND preg_id = %s"
                            count_params.append(value)

            cursor.execute(count_sql, count_params)
            total_with_remarks = cursor.fetchone()['total']
            print(f"📊 数据库中有备注的记录总数: {total_with_remarks:,} 条")

            if total_with_remarks == 0:
                print("❌ 没有找到有备注的数据")
                return []

            # 调整采样大小
            actual_sample_size = min(config.sample_size, total_with_remarks)
            if actual_sample_size < config.sample_size:
                print(f"⚠️  有备注数据不足，调整采样大小为 {actual_sample_size} 条")

            # 采样不同的备注内容（确保多样性）
            sql = """
            SELECT DISTINCT paymentdetail_note as remark_content
            FROM {}
            WHERE paymentdetail_note IS NOT NULL AND paymentdetail_note != '' AND paymentdetail_note != '[]'
                  AND paymentdetail_note != 'null' AND paymentdetail_note != '无' AND paymentdetail_note != '退回'
                  AND paymentdetail_note != '0' AND paymentdetail_note != '1' AND LENGTH(paymentdetail_note) > 3
            """.format(table_name)

            # 添加过滤条件（如果有）
            sample_params = []
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            sample_params.append(year_month)
                        elif key == 'preg_id':
                            sql += " AND preg_id = %s"
                            sample_params.append(value)

            # 随机排序并限制数量
            sql += f" ORDER BY RAND() LIMIT {actual_sample_size}"

            print(f"执行采样SQL（获取不同的备注内容）...")
            cursor.execute(sql, sample_params)
            results = cursor.fetchall()

            print(f"✅ 采样完成，获取到 {len(results)} 条不同的备注内容")

            # 直接返回备注内容列表，而不是ExpenseRecord对象
            remarks = [row['remark_content'] for row in results if row['remark_content']]

            return remarks

    def _has_remark_content(self, record) -> bool:
        """检查记录是否有实际备注内容"""
        # 检查备注（支持JSON格式和直接字符串格式）
        if record.paymentdetail_note and record.paymentdetail_note.strip():
            try:
                # 尝试解析JSON格式
                from src.expense_analysis.models import RemarkDetail
                remark_details = RemarkDetail.from_json(record.paymentdetail_note)
                for detail in remark_details:
                    if detail.content and detail.content.strip():
                        return True
            except:
                # 如果不是JSON格式，直接作为字符串处理
                return True

        # 检查必填备注
        if record.mandatory_note and record.mandatory_note.strip():
            return True

        return False
    
    def _sample_by_city(self, cursor, table_name: str, filters: Dict[str, Any], 
                       sample_size: int) -> List[ExpenseRecord]:
        """按地市分层采样"""
        # 获取所有地市及其记录数
        sql = f"""
        SELECT preg_id, preg_name, COUNT(*) as count 
        FROM {table_name} 
        WHERE preg_id IS NOT NULL AND preg_name IS NOT NULL
        """
        
        params = []
        if filters:
            conditions = []
            for key, value in filters.items():
                if value is not None:
                    conditions.append(f"{key} = %s")
                    params.append(value)
            
            if conditions:
                sql += " AND " + " AND ".join(conditions)
        
        sql += " GROUP BY preg_id, preg_name"
        
        cursor.execute(sql, params)
        cities = cursor.fetchall()
        
        if not cities:
            return []
        
        total_records = sum(city['count'] for city in cities)
        samples = []
        
        for city in cities:
            # 按比例分配样本
            city_sample_size = max(1, int(sample_size * city['count'] / total_records))
            city_samples = self._random_sample_by_condition(
                cursor, table_name, filters, city_sample_size,
                additional_condition=f"preg_id = '{city['preg_id']}'"
            )
            samples.extend(city_samples)
        
        return samples
    
    def _sample_by_month(self, cursor, table_name: str, filters: Dict[str, Any],
                        sample_size: int) -> List[ExpenseRecord]:
        """按月份分层采样"""
        # 获取所有月份及其记录数，使用billamount_startdate
        sql = """
        SELECT DATE_FORMAT(billamount_startdate, '%%Y%%m') as rpt_month, COUNT(*) as count
        FROM {}
        WHERE billamount_startdate IS NOT NULL
        """.format(table_name)

        params = []
        if filters:
            conditions = []
            for key, value in filters.items():
                if value is not None:
                    if key == 'rpt_month':
                        # 处理rpt_month筛选
                        year = value[:4]
                        month = value[4:6]
                        year_month = f"{year}-{month}"
                        conditions.append("(%s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m'))")
                        params.append(year_month)
                    else:
                        conditions.append(f"{key} = %s")
                        params.append(value)

            if conditions:
                sql += " AND " + " AND ".join(conditions)

        sql += " GROUP BY DATE_FORMAT(billamount_startdate, '%%Y%%m')"

        cursor.execute(sql, params)
        months = cursor.fetchall()

        if not months:
            return []

        total_records = sum(month['count'] for month in months)
        samples = []

        for month in months:
            # 按比例分配样本
            month_sample_size = max(1, int(sample_size * month['count'] / total_records))
            month_samples = self._random_sample_by_condition(
                cursor, table_name, filters, month_sample_size,
                additional_condition="DATE_FORMAT(billamount_startdate, '%%Y%%m') = '{}'".format(month['rpt_month'])
            )
            samples.extend(month_samples)

        return samples

    def _random_sample_by_condition(self, cursor, table_name: str, filters: Dict[str, Any],
                                   sample_size: int, additional_condition: str = None,
                                   exclude_ids: List[str] = None) -> List[ExpenseRecord]:
        """根据条件随机采样"""
        sql = """
        SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
               reg_id, reg_name, billamount_date, paymentdetail_note,
               mandatory_note, search_keywords, auditing_state,
               last_review_result, last_review_comment, billamount_startdate, billamount_enddate
        FROM {}
        WHERE 1=1
        """.format(table_name)

        params = []

        # 添加基础过滤条件
        if filters:
            for key, value in filters.items():
                if value is not None:
                    if key == 'rpt_month':
                        # 处理rpt_month筛选，转换为年月格式
                        year = value[:4]
                        month = value[4:6]
                        year_month = f"{year}-{month}"
                        sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                        params.append(year_month)
                    else:
                        sql += f" AND {key} = %s"
                        params.append(value)

        # 添加额外条件
        if additional_condition:
            sql += f" AND {additional_condition}"

        # 排除已选择的ID
        if exclude_ids:
            placeholders = ','.join(['%s'] * len(exclude_ids))
            sql += f" AND billaccountpaymentdetail_id NOT IN ({placeholders})"
            params.extend(exclude_ids)

        # 随机排序并限制数量
        sql += f" ORDER BY RAND() LIMIT {sample_size}"

        cursor.execute(sql, params)
        results = cursor.fetchall()

        return [self._dict_to_expense_record(row) for row in results]

    def _random_sample(self, cursor, table_name: str, filters: Dict[str, Any],
                      sample_size: int, exclude_ids: List[str] = None) -> List[ExpenseRecord]:
        """随机采样"""
        return self._random_sample_by_condition(cursor, table_name, filters, sample_size,
                                               exclude_ids=exclude_ids)

    def _dict_to_expense_record(self, row: Dict[str, Any], target_month: str = None) -> ExpenseRecord:
        """将数据库行转换为ExpenseRecord对象"""
        # 使用目标月份作为rpt_month，而不是从日期计算
        rpt_month = target_month

        return ExpenseRecord(
            billaccountpaymentdetail_id=row['billaccountpaymentdetail_id'],
            payment_code=row.get('payment_code'),
            preg_id=row.get('preg_id'),
            preg_name=row.get('preg_name'),
            reg_id=row.get('reg_id'),
            reg_name=row.get('reg_name'),
            billamount_date=row.get('billamount_date'),
            paymentdetail_note=row.get('paymentdetail_note'),
            mandatory_note=row.get('mandatory_note'),
            search_keywords=row.get('search_keywords'),
            auditing_state=row.get('auditing_state'),
            last_review_result=row.get('last_review_result'),
            last_review_comment=row.get('last_review_comment'),
            rpt_month=rpt_month,
            billamount_startdate=row.get('billamount_startdate'),
            billamount_enddate=row.get('billamount_enddate')
        )

    def get_comprehensive_remarks_for_classification(self, records: List[ExpenseRecord]) -> List[Dict]:
        """提取记录的综合备注信息，用于综合分析"""
        comprehensive_remarks = []
        json_parse_failed = 0
        empty_content_count = 0

        for record in records:
            # 收集单条记录的所有备注信息
            record_remarks = {
                'record_id': record.billaccountpaymentdetail_id,
                'payment_code': record.payment_code,
                'details': [],
                'mandatory_note': record.mandatory_note or '',
                'combined_content': []
            }

            # 按产品需求：直接连接3个字段的字符串内容
            combined_text_parts = []

            # 1. 关键字 - 只有非空且不是无效值才添加
            if (record.search_keywords and record.search_keywords.strip() and
                record.search_keywords.strip() not in ['null', '无', '', '[]']):
                combined_text_parts.append(f"关键字: {record.search_keywords.strip()}")

            # 2. 必填备注 - 只有非空且不是无效值才添加
            if (record.mandatory_note and record.mandatory_note.strip() and
                record.mandatory_note.strip() not in ['null', '无', '', '[]']):
                combined_text_parts.append(f"必填备注: {record.mandatory_note.strip()}")

            # 3. 备注 - 特殊处理JSON格式
            if record.paymentdetail_note and record.paymentdetail_note.strip():
                raw_note = record.paymentdetail_note.strip()
                if raw_note not in ['null', '无', '', '[]']:
                    # 尝试解析JSON格式，提取content内容
                    try:
                        import json
                        if raw_note.startswith('[') and raw_note.endswith(']'):
                            # JSON数组格式
                            json_data = json.loads(raw_note)
                            content_parts = []
                            for item in json_data:
                                if isinstance(item, dict) and 'content' in item:
                                    content = item.get('content', '').strip()
                                    if content and content not in ['null', '无', '', '0']:
                                        # 添加更多上下文信息
                                        dict_name = item.get('dict_name', '')
                                        dictgroup_name = item.get('dictgroup_name', '')
                                        if dict_name or dictgroup_name:
                                            content_parts.append(f"[{dictgroup_name}>{dict_name}] {content}")
                                        else:
                                            content_parts.append(content)

                            if content_parts:
                                combined_text_parts.append(f"备注: {'; '.join(content_parts)}")
                            # 如果JSON解析成功但没有有效内容，不添加
                        else:
                            # 非JSON格式，直接使用
                            combined_text_parts.append(f"备注: {raw_note}")
                    except (json.JSONDecodeError, Exception):
                        # JSON解析失败，直接使用原始内容
                        combined_text_parts.append(f"备注: {raw_note}")

            # 连接所有内容 - 只有真正有内容时才连接
            if combined_text_parts:
                combined_content = " | ".join(combined_text_parts)
            else:
                combined_content = "无备注信息"

            record_remarks['combined_content'] = [combined_content]
            record_remarks['details'] = [{
                'count': 1,
                'dictgroup_name': '',
                'dict_name': '',
                'content': combined_content
            }]

            comprehensive_remarks.append(record_remarks)

        # 详细统计分析
        has_search_keywords = 0
        has_mandatory_note = 0
        has_paymentdetail_note = 0
        truly_empty = 0
        json_parsed_count = 0
        json_with_content = 0

        for record in records:
            has_sk = (record.search_keywords and record.search_keywords.strip() and
                     record.search_keywords.strip() not in ['null', '无', '', '[]'])
            has_mn = (record.mandatory_note and record.mandatory_note.strip() and
                     record.mandatory_note.strip() not in ['null', '无', '', '[]'])

            # 特殊处理paymentdetail_note的JSON格式
            has_pn = False
            if record.paymentdetail_note and record.paymentdetail_note.strip():
                raw_note = record.paymentdetail_note.strip()
                if raw_note not in ['null', '无', '', '[]']:
                    try:
                        import json
                        if raw_note.startswith('[') and raw_note.endswith(']'):
                            json_parsed_count += 1
                            json_data = json.loads(raw_note)
                            for item in json_data:
                                if isinstance(item, dict) and 'content' in item:
                                    content = item.get('content', '').strip()
                                    if content and content not in ['null', '无', '', '0']:
                                        has_pn = True
                                        json_with_content += 1
                                        break
                        else:
                            has_pn = True
                    except:
                        has_pn = True

            if has_sk: has_search_keywords += 1
            if has_mn: has_mandatory_note += 1
            if has_pn: has_paymentdetail_note += 1
            if not (has_sk or has_mn or has_pn): truly_empty += 1

        # 输出详细统计信息
        print(f"📊 备注处理统计:")
        print(f"   总记录数: {len(records)}")
        print(f"   处理记录数: {len(comprehensive_remarks)}")
        print(f"   数据完整性: {'✅ 完整' if len(comprehensive_remarks) == len(records) else '❌ 不完整'}")
        print(f"📝 字段统计:")
        print(f"   有关键字: {has_search_keywords} 条")
        print(f"   有必填备注: {has_mandatory_note} 条")
        print(f"   有备注: {has_paymentdetail_note} 条")
        print(f"   JSON格式备注: {json_parsed_count} 条")
        print(f"   JSON有效内容: {json_with_content} 条")
        print(f"   真正无备注: {truly_empty} 条")

        return comprehensive_remarks

    def get_unclassified_records(self, limit: int = 1000) -> List[ExpenseRecord]:
        """获取未分类的记录"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            # 查询未分类的记录（假设category字段为空或NULL表示未分类）
            query = """
                SELECT * FROM expense_records
                WHERE (category IS NULL OR category = '' OR category = '未分类')
                LIMIT %s
            """

            cursor.execute(query, (limit,))
            rows = cursor.fetchall()

            records = []
            for row in rows:
                record = self._dict_to_expense_record(row)
                records.append(record)

            cursor.close()
            connection.close()

            return records

        except Exception as e:
            print(f"获取未分类记录失败: {e}")
            return []

    def get_records_for_classification(self, table_name: str, filters: Dict = None, limit: int = None) -> List[ExpenseRecord]:
        """获取需要分类的记录"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            # 构建查询SQL，使用clean_ele_payment表的字段名
            sql = f"""
                SELECT billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
                       reg_id, reg_name, billamount_date, paymentdetail_note,
                       mandatory_note, search_keywords, auditing_state,
                       billamount_startdate, billamount_enddate
                FROM {table_name}
                WHERE (
                    (paymentdetail_note IS NOT NULL AND paymentdetail_note != '' AND paymentdetail_note != '[]'
                     AND paymentdetail_note != 'null' AND paymentdetail_note != '无' AND paymentdetail_note != '退回'
                     AND paymentdetail_note != '0' AND paymentdetail_note != '1' AND LENGTH(paymentdetail_note) > 3)
                    OR (mandatory_note IS NOT NULL AND mandatory_note != '' AND mandatory_note != 'null'
                        AND mandatory_note != '无' AND mandatory_note != '退回' AND LENGTH(mandatory_note) > 3)
                )
            """

            params = []
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            year = value[:4]
                            month = value[4:6]
                            year_month = f"{year}-{month}"
                            sql += " AND %s BETWEEN DATE_FORMAT(billamount_startdate, '%%Y-%%m') AND DATE_FORMAT(billamount_enddate, '%%Y-%%m')"
                            params.append(year_month)
                        else:
                            sql += f" AND {key} = %s"
                            params.append(value)

            if limit:
                sql += f" LIMIT {limit}"

            cursor.execute(sql, params)
            rows = cursor.fetchall()

            records = []
            for row in rows:
                # 使用原有的_dict_to_expense_record方法
                record = self._dict_to_expense_record(row)
                records.append(record)

            cursor.close()
            connection.close()

            return records

        except Exception as e:
            print(f"获取分类记录失败: {e}")
            return []

    def clear_classification_results(self, filters: Dict = None):
        """清理分类结果"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # 使用正确的分类结果表名
            sql = "DELETE FROM ele_payment_ai_sort WHERE 1=1"
            params = []

            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            sql += " AND rpt_month = %s"
                            params.append(value)
                        else:
                            sql += f" AND {key} = %s"
                            params.append(value)

            cursor.execute(sql, params)
            connection.commit()

            cursor.close()
            connection.close()

            print(f"清理分类结果完成，条件: {filters}")
            return True

        except Exception as e:
            print(f"清理分类结果失败: {e}")
            return False

    def save_classification_result(self, record_id: str, category: str):
        """保存分类结果"""
        try:
            connection = self.get_connection()
            cursor = connection.cursor()

            # 使用正确的分类结果表名和字段
            query = """
                INSERT INTO ele_payment_ai_sort
                (billaccountpaymentdetail_id, ai_category, ai_confidence, ai_classified_time)
                VALUES (%s, %s, 0.8, NOW())
                ON DUPLICATE KEY UPDATE
                ai_category = VALUES(ai_category),
                ai_confidence = VALUES(ai_confidence),
                ai_classified_time = VALUES(ai_classified_time)
            """

            cursor.execute(query, (record_id, category))
            connection.commit()

            cursor.close()
            connection.close()

            return True

        except Exception as e:
            print(f"保存分类结果失败: {e}")
            return False

    def update_classification(self, record_id: str, category: str):
        """更新记录的分类（兼容旧接口）"""
        return self.save_classification_result(record_id, category)

    def get_all_remarks_for_classification(self, records: List[ExpenseRecord]) -> List[str]:
        """提取所有需要分类的备注内容（用于发现分类）"""
        remarks = []

        for record in records:
            # 处理备注（支持JSON格式和直接字符串格式）
            if record.paymentdetail_note and record.paymentdetail_note.strip():
                try:
                    # 尝试解析JSON格式
                    from src.expense_analysis.models import RemarkDetail
                    remark_details = RemarkDetail.from_json(record.paymentdetail_note)
                    for detail in remark_details:
                        if detail.content and detail.content.strip():
                            remarks.append(detail.content.strip())
                except:
                    # 如果不是JSON格式，直接作为字符串处理
                    remarks.append(record.paymentdetail_note.strip())

            # 处理必填备注
            if record.mandatory_note and record.mandatory_note.strip():
                remarks.append(record.mandatory_note.strip())

        # 不去重，保留所有备注用于AI分析（重复的备注也有价值）
        print(f"✅ 提取到 {len(remarks)} 条备注内容（包含重复）")

        # 如果需要查看去重后的数量
        unique_count = len(set(remarks))
        if unique_count < len(remarks):
            print(f"📊 去重后有 {unique_count} 条不同的备注内容")

        return remarks

    def save_classification_results(self, source_table: str, records: List[ExpenseRecord],
                                  classification_results: Dict[str, Tuple[str, float]]):
        """保存分类结果到新表"""
        if not classification_results:
            return

        # 确保分类结果表存在
        self._ensure_classification_table()

        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 准备插入数据
            insert_data = []
            current_time = datetime.now()

            for record in records:
                # 收集该记录的所有备注
                original_remarks = []

                if record.paymentdetail_note:
                    from ..models import RemarkDetail
                    remark_details = RemarkDetail.from_json(record.paymentdetail_note)
                    for detail in remark_details:
                        if detail.content and detail.content.strip():
                            original_remarks.append(detail.content.strip())

                if record.mandatory_note and record.mandatory_note.strip():
                    original_remarks.append(record.mandatory_note.strip())

                # 找到最佳分类
                best_category = "其他费用"
                best_confidence = 0.0

                for remark in original_remarks:
                    if remark in classification_results:
                        result = classification_results[remark]
                        if result[1] > best_confidence:
                            best_category = result[0]
                            best_confidence = result[1]

                # 准备插入数据
                insert_data.append((
                    record.billaccountpaymentdetail_id,
                    record.payment_code,
                    record.preg_id,
                    record.preg_name,
                    record.reg_id,
                    record.reg_name,
                    record.rpt_month,
                    '; '.join(original_remarks),  # 合并所有备注
                    best_category,
                    best_confidence,
                    current_time,
                    record.billamount_startdate,
                    record.billamount_enddate,
                    record.auditing_state
                ))

            # 批量插入或更新
            sql = """
            INSERT INTO ele_payment_ai_sort
            (billaccountpaymentdetail_id, payment_code, preg_id, preg_name,
             reg_id, reg_name, rpt_month, original_remark, ai_category,
             ai_confidence, ai_classified_time, billamount_startdate, billamount_enddate, auditing_state)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            ai_category = VALUES(ai_category),
            ai_confidence = VALUES(ai_confidence),
            ai_classified_time = VALUES(ai_classified_time),
            auditing_state = VALUES(auditing_state),
            original_remark = VALUES(original_remark),
            billamount_startdate = VALUES(billamount_startdate),
            billamount_enddate = VALUES(billamount_enddate)
            """

            cursor.executemany(sql, insert_data)
            conn.commit()

            print(f"✅ 已保存 {len(insert_data)} 条分类结果到 ele_payment_ai_sort 表")

    def _ensure_classification_table(self):
        """确保分类结果表存在"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 检查表是否存在
            cursor.execute("SHOW TABLES LIKE 'ele_payment_ai_sort'")
            if not cursor.fetchone():
                print("⚠️  分类结果表 ele_payment_ai_sort 不存在")
                print("请先执行建表SQL创建该表，详见README.md")
                raise Exception("分类结果表不存在，请先创建表")

            print("✅ 分类结果表 ele_payment_ai_sort 已存在")

    def get_classification_statistics(self, filters: Dict[str, Any] = None) -> Dict[str, int]:
        """获取分类统计结果"""
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            sql = """
            SELECT ai_category, COUNT(*) as count
            FROM ele_payment_ai_sort
            WHERE 1=1
            """

            params = []
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            sql += " AND rpt_month = %s"
                            params.append(value)
                        elif key == 'preg_id':
                            sql += " AND preg_id = %s"
                            params.append(value)

            sql += " GROUP BY ai_category ORDER BY count DESC"

            cursor.execute(sql, params)
            results = cursor.fetchall()

            return {row['ai_category']: row['count'] for row in results}

    def get_classification_details(self, category: str, filters: Dict[str, Any] = None) -> List[Dict]:
        """获取指定分类的明细数据"""
        with self.get_connection() as conn:
            cursor = conn.cursor(pymysql.cursors.DictCursor)

            sql = """
            SELECT billaccountpaymentdetail_id, payment_code, preg_name, reg_name,
                   rpt_month, original_remark, ai_category, ai_confidence
            FROM ele_payment_ai_sort
            WHERE ai_category = %s
            """

            params = [category]
            if filters:
                for key, value in filters.items():
                    if value is not None:
                        if key == 'rpt_month':
                            sql += " AND rpt_month = %s"
                            params.append(value)
                        elif key == 'preg_id':
                            sql += " AND preg_id = %s"
                            params.append(value)

            sql += " ORDER BY ai_confidence DESC"

            cursor.execute(sql, params)
            return cursor.fetchall()
