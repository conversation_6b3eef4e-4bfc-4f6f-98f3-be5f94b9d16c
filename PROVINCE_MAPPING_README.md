# 省份感知数据库查询功能

## 功能概述

本功能实现了从请求体的system消息中自动提取省份代码，并根据省份代码动态选择对应的数据库进行SQL查询。

## 核心特性

1. **自动省份提取**: 从system消息中提取省份代码（如GZ、BJ等）
2. **动态数据库选择**: 根据省份代码自动选择对应的数据库
3. **环境配置化**: 省份映射配置存储在环境文件中，支持灵活配置
4. **向后兼容**: 如果未提供省份代码，自动使用默认数据库

## 配置说明

### 1. 环境变量配置

在`.env`文件中添加省份数据库映射配置：

```bash
# 省份数据库映射配置
# 格式：省份代码:数据库名称:省份名称,省份代码:数据库名称:省份名称,...
PROVINCE_DATABASE_MAPPING=NX:analysis_nx:宁夏,GS:analysis_gs:甘肃,HE:analysis_he:河北,GZ:analysis_gz:贵州,XXL:xxltidb:特殊区域,JT:analysis_qg:总部,BJ:analysis_bj:北京,TJ:analysis_tj:天津,SX:analysis_sx:山西,NM:analysis_nm:内蒙古,LN:analysis_ln:辽宁,JL:analysis_jl:吉林,HL:analysis_hl:黑龙江,SH:analysis_sh:上海,JS:analysis_js:江苏,ZJ:analysis_zj:浙江,AH:analysis_ah:安徽,FJ:analysis_fj:福建,JX:analysis_jx:江西,SD:analysis_sd:山东,HA:analysis_ha:河南,HB:analysis_hb:湖北,HN:analysis_hn:湖南,GD:analysis_gd:广东,GX:analysis_gx:广西,HI:analysis_hi:海南,CQ:analysis_cq:重庆,SC:analysis_sc:四川,YN:analysis_yn:云南,XZ:analysis_xz:西藏,SN:analysis_sn:陕西,QH:analysis_qh:青海,XJ:analysis_xj:新疆
```

### 2. 支持的省份代码

| 省份代码 | 数据库名称 | 省份名称 |
|---------|-----------|----------|
| GZ | analysis_gz | 贵州 |
| BJ | analysis_bj | 北京 |
| SH | analysis_sh | 上海 |
| XXL | xxltidb | 特殊区域 |
| JT | analysis_qg | 总部 |
| ... | ... | ... |

## 使用方法

### 1. API请求格式

在请求体的`messages`中包含system消息，格式为`数据库:省份代码`：

```json
{
  "model": "analysis_agent",
  "messages": [
    {
      "role": "system", 
      "content": "数据库:GZ"
    },
    {
      "role": "user", 
      "content": "2025年4月铁塔服务费是多少"
    }
  ],
  "temperature": 0.7,
  "stream": false
}
```

### 2. 支持的system消息格式

- `数据库:GZ`
- `数据库：BJ` (中文冒号)
- `数据库: SH` (带空格)
- `数据库 : XXL` (多空格)

### 3. 处理流程

1. **提取省份代码**: 从system消息中提取省份代码
2. **验证映射**: 检查省份代码是否在配置的映射中
3. **选择数据库**: 根据映射选择对应的数据库名称
4. **执行查询**: 使用选定的数据库执行SQL查询
5. **返回结果**: 返回查询结果

## 实现细节

### 1. 核心组件

- `ProvinceAwareAgent`: 省份感知的Agent类
- `IntegratedSQLTool`: 支持动态数据库选择的SQL工具
- `DatabaseConfig`: 支持动态数据库名称的配置类

### 2. 关键文件

- `src/agents/province_aware_agent.py`: 省份感知Agent实现
- `src/tools/integrated_sql_tools.py`: 集成SQL工具（已修改）
- `src/config/database_config.py`: 数据库配置（已修改）
- `src/config/settings.py`: 设置配置（已修改）
- `examples/analysis_agent_server.py`: API服务器（已修改）

### 3. 日志记录

系统会记录详细的省份提取和数据库选择日志：

```
🌍 [省份Agent] 接收到系统消息: 数据库:GZ
🎯 [省份Agent] 提取到省份代码: GZ
✅ [省份Agent] 省份代码 GZ 对应数据库: analysis_gz
🔧 [省份Agent] 为integrated_sql工具自动添加system_message参数
🎯 [数据库选择] 省份代码 GZ 映射到数据库: analysis_gz
```

## 测试

### 1. 运行基础测试

```bash
python test_province_extraction.py
```

### 2. 运行端到端测试

```bash
# 先启动API服务器
python examples/analysis_agent_server.py

# 在另一个终端运行测试
python test_end_to_end.py
```

### 3. 手动测试

使用curl或Postman发送请求：

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "analysis_agent",
    "messages": [
      {"role": "system", "content": "数据库:GZ"},
      {"role": "user", "content": "2025年4月电费是多少"}
    ],
    "stream": false
  }'
```

## 故障排除

### 1. 省份代码未识别

- 检查system消息格式是否正确
- 确认省份代码在`PROVINCE_DATABASE_MAPPING`中存在
- 查看日志中的省份提取信息

### 2. 数据库连接失败

- 检查对应数据库的连接配置
- 确认数据库服务器可访问
- 验证数据库名称是否正确

### 3. 默认数据库使用

如果未提供省份代码或提取失败，系统会自动使用默认数据库（`MYSQL_DATABASE`环境变量）。

## 扩展说明

### 1. 添加新省份

在`.env`文件的`PROVINCE_DATABASE_MAPPING`中添加新的映射：

```bash
PROVINCE_DATABASE_MAPPING=现有映射,NEW:analysis_new:新省份
```

### 2. 修改映射

直接修改`.env`文件中的映射配置，重启服务即可生效。

### 3. 自定义提取规则

修改`ProvinceAwareAgent._extract_province_code`方法来支持新的提取规则。
