#!/usr/bin/env python3
"""
费用分析API服务安全启动脚本
支持优雅关闭，避免端口占用问题
"""
import os
import sys
import signal
import atexit
import psutil
import time
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

# 全局变量
server_process = None
pid_file = Path("api_server.pid")

def cleanup_processes():
    """清理所有相关进程"""
    print("\n🧹 正在清理进程...")
    
    # 清理PID文件中记录的进程
    if pid_file.exists():
        try:
            with open(pid_file, 'r') as f:
                pids = [int(line.strip()) for line in f.readlines() if line.strip()]
            
            for pid in pids:
                try:
                    process = psutil.Process(pid)
                    if process.is_running():
                        print(f"🔄 终止进程 PID: {pid}")
                        process.terminate()
                        # 等待进程优雅退出
                        try:
                            process.wait(timeout=5)
                        except psutil.TimeoutExpired:
                            print(f"⚡ 强制终止进程 PID: {pid}")
                            process.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            pid_file.unlink()
            print("✅ PID文件已清理")
        except Exception as e:
            print(f"⚠️ 清理PID文件时出错: {e}")
    
    # 查找并清理占用8002端口的进程
    cleanup_port_8002()

def cleanup_port_8002():
    """清理占用8002端口的进程"""
    print("🔍 检查8002端口占用...")
    
    try:
        for conn in psutil.net_connections(kind='inet'):
            if conn.laddr.port == 8002 and conn.status == psutil.CONN_LISTEN:
                try:
                    process = psutil.Process(conn.pid)
                    print(f"🎯 发现占用8002端口的进程: PID {conn.pid} ({process.name()})")
                    
                    # 检查是否是我们的进程
                    cmdline = ' '.join(process.cmdline())
                    if 'expense_analysis' in cmdline or 'uvicorn' in cmdline:
                        print(f"🔄 终止相关进程: {process.name()}")
                        process.terminate()
                        try:
                            process.wait(timeout=5)
                        except psutil.TimeoutExpired:
                            print(f"⚡ 强制终止进程: {process.name()}")
                            process.kill()
                        print("✅ 端口已释放")
                    else:
                        print(f"⚠️ 端口被其他程序占用: {process.name()}")
                except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                    print(f"⚠️ 无法访问进程: {e}")
    except Exception as e:
        print(f"⚠️ 检查端口时出错: {e}")

def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n📡 收到信号 {signum}")
    cleanup_processes()
    sys.exit(0)

def save_pid(pid):
    """保存进程ID到文件"""
    try:
        with open(pid_file, 'a') as f:
            f.write(f"{pid}\n")
    except Exception as e:
        print(f"⚠️ 保存PID失败: {e}")

def check_port_available():
    """检查端口是否可用"""
    for conn in psutil.net_connections(kind='inet'):
        if conn.laddr.port == 8002 and conn.status == psutil.CONN_LISTEN:
            return False, conn.pid
    return True, None

def main():
    """启动API服务"""
    print("💰 启动费用分析API服务 (安全模式)")
    print("=" * 50)
    print("📍 端口: 8002")
    print("📖 文档: http://localhost:8002/docs")
    print("🔧 模式: 安全启动 (支持优雅关闭)")
    print("🛑 终止: Ctrl+C 或发送 SIGTERM 信号")
    print("=" * 50)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
    
    # 注册退出清理
    atexit.register(cleanup_processes)
    
    # 检查端口是否被占用
    available, occupying_pid = check_port_available()
    if not available:
        print(f"❌ 端口8002已被进程 {occupying_pid} 占用")
        print("🧹 正在清理占用端口的进程...")
        cleanup_port_8002()
        time.sleep(2)
        
        # 再次检查
        available, occupying_pid = check_port_available()
        if not available:
            print(f"❌ 无法释放端口8002，请手动处理进程 {occupying_pid}")
            return
    
    try:
        import uvicorn
        import logging
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 禁用不必要的日志
        logging.getLogger("watchfiles").setLevel(logging.WARNING)
        logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
        
        print("🚀 正在启动服务...")
        
        # 保存当前进程PID
        save_pid(os.getpid())
        
        # 启动服务
        uvicorn.run(
            "src.expense_analysis.api.export_api:app",
            host="0.0.0.0",
            port=8002,
            reload=True,
            log_level="info",
            reload_excludes=[
                "*.log", "*.tmp", "*.cache", "__pycache__/*",
                "*.pyc", "*.pyo", ".git/*", ".vscode/*",
                "*.xlsx", "*.json", "logs/*", "*.pid"
            ]
        )
        
    except ImportError:
        print("❌ 错误: 未安装uvicorn")
        print("请运行: pip install uvicorn")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 收到中断信号，正在优雅关闭...")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
    finally:
        cleanup_processes()

if __name__ == "__main__":
    main()
