# 向量增强智能分析系统

## 🎯 系统概述

本系统实现了基于ChromaDB向量数据库的智能提示词管理和检索功能，能够根据用户问题自动匹配最合适的处理流程，实现"二次训练"的效果。

## 🚀 核心功能

### 1. 智能问题匹配
- 用户提问时自动检索向量数据库
- 基于语义相似度匹配最合适的复杂提示词
- 如无匹配结果，回退到默认处理逻辑

### 2. 复杂提示词管理
- 支持创建、编辑、删除复杂提示词
- 每个提示词包含：标题、描述、触发关键词、处理步骤、回复格式
- 支持优先级设置，高优先级提示词优先匹配

### 3. Web管理界面
- 直观的Web界面管理复杂提示词
- 支持搜索测试功能，验证匹配效果
- 实时统计信息展示

### 4. 省份感知功能
- 从system消息中自动提取省份代码
- 动态选择对应的数据库进行查询
- 支持33个省份/区域的完整映射

## 📋 系统架构

```
用户问题
    ↓
向量数据库检索 (ChromaDB)
    ↓
匹配复杂提示词？
    ↓ 是              ↓ 否
使用复杂提示词    →  使用默认逻辑
    ↓                  ↓
省份代码提取
    ↓
动态数据库选择
    ↓
执行SQL查询
    ↓
返回结构化结果
```

## 🔧 配置说明

### 1. Embedding模型配置

在`.env`文件中配置您的远程embedding服务：

```bash
# 启用远程embedding服务
REMOTE_EMBEDDING_ENABLED=true
# 您的embedding服务URL
REMOTE_EMBEDDING_URL=http://your-embedding-service:8001/embeddings
# API密钥（如需要）
REMOTE_EMBEDDING_API_KEY=your-api-key
```

### 2. 省份数据库映射

```bash
# 省份代码到数据库的映射配置
PROVINCE_DATABASE_MAPPING=GZ:analysis_gz:贵州,BJ:analysis_bj:北京,SH:analysis_sh:上海,...
```

## 🛠️ 使用指南

### 1. 启动系统

```bash
# 启动API服务器
python examples/analysis_agent_server.py
```

### 2. 访问管理界面

打开浏览器访问：`http://localhost:8000/admin`

### 3. 创建复杂提示词

在管理界面中点击"创建提示词"，填写以下信息：

- **标题**：提示词的名称
- **描述**：提示词的用途说明
- **触发关键词**：用逗号分隔的关键词列表
- **处理步骤**：每行一个处理步骤
- **回复格式**：期望的回复格式模板
- **优先级**：1-4，数字越大优先级越高

### 4. 测试匹配效果

在"搜索测试"标签页中输入用户可能的问题，查看匹配结果和相似度分数。

### 5. API调用示例

```json
{
  "model": "analysis_agent",
  "messages": [
    {"role": "system", "content": "数据库:GZ"},
    {"role": "user", "content": "当前电费情况如何"}
  ],
  "stream": false
}
```

## 📊 示例复杂提示词

### 电费情况综合分析

**触发关键词**：电费情况、电费分析、电费统计

**处理步骤**：
1. 查询最近3个月的电费数据
2. 分析电费构成和趋势
3. 对比历史数据评估合理性
4. 提供优化建议

**回复格式**：
```
# 📊 电费情况综合分析报告
## 📈 数据概览
## 📋 详细数据
## 📊 趋势分析
## 💡 优化建议
```

### 铁塔服务费深度分析

**触发关键词**：铁塔服务费、塔类服务费、铁塔费用

**处理步骤**：
1. 查询铁塔服务费总额和明细
2. 分析费用构成和占比
3. 对比历史数据分析趋势
4. 提供成本优化建议

## 🔍 API接口

### 复杂提示词管理

- `GET /api/prompts` - 获取所有提示词
- `POST /api/prompts` - 创建新提示词
- `PUT /api/prompts/{id}` - 更新提示词
- `DELETE /api/prompts/{id}` - 删除提示词
- `POST /api/prompts/search` - 搜索提示词

### 聊天接口

- `POST /v1/chat/completions` - 兼容OpenAI格式的聊天接口

### 管理界面

- `GET /admin` - Web管理界面
- `GET /static/*` - 静态文件服务

## 📈 工作流程

### 1. 用户提问流程

```
用户提问 → 向量检索 → 匹配提示词 → 执行处理步骤 → 返回结构化结果
```

### 2. 提示词匹配逻辑

1. **语义检索**：使用embedding模型将用户问题向量化
2. **相似度计算**：计算与存储提示词的相似度
3. **阈值过滤**：过滤低于阈值的结果
4. **优先级排序**：按优先级和相似度排序
5. **应用提示词**：使用最佳匹配的提示词

### 3. 省份感知处理

1. **提取省份代码**：从system消息中提取省份代码
2. **数据库映射**：根据省份代码选择对应数据库
3. **动态查询**：使用选定数据库执行SQL查询

## 🧪 测试验证

### 1. 创建示例数据

```bash
python examples/sample_complex_prompts.py
```

### 2. 运行完整测试

```bash
python test_vector_enhanced_system.py
```

### 3. 手动测试

使用curl测试API：

```bash
curl -X POST http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "analysis_agent",
    "messages": [
      {"role": "system", "content": "数据库:GZ"},
      {"role": "user", "content": "当前电费情况如何"}
    ]
  }'
```

## 🔧 自定义扩展

### 1. 添加新的处理流程

在管理界面中创建新的复杂提示词，定义：
- 触发条件（关键词）
- 处理步骤
- 回复格式

### 2. 集成自定义Embedding模型

修改`src/core/prompt_vectorstore_manager.py`中的`_initialize_vectorstore`方法，集成您的embedding服务。

### 3. 扩展省份映射

在`.env`文件中的`PROVINCE_DATABASE_MAPPING`中添加新的省份映射。

## 📝 注意事项

1. **Embedding模型**：需要配置您的远程embedding服务
2. **数据库连接**：确保各省份数据库连接配置正确
3. **向量存储**：ChromaDB数据存储在`./data/prompt_chroma_db`目录
4. **性能优化**：建议根据实际使用情况调整相似度阈值

## 🎯 预期效果

- **智能匹配**：根据用户问题自动选择最合适的处理流程
- **结构化输出**：按照预定义格式返回专业的分析报告
- **省份感知**：自动识别省份并查询对应数据库
- **易于管理**：通过Web界面轻松管理复杂提示词

这个系统实现了您要求的"二次训练"效果，通过向量数据库存储复杂提示词，让AI能够根据不同问题类型提供更专业、更结构化的回答。
