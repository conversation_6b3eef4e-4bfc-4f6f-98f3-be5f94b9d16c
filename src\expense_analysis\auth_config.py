"""
费用分析模块鉴权配置
"""
import os
from typing import Set

# API 鉴权配置
class AuthConfig:
    """鉴权配置"""
    
    # 有效的API Token列表 (可以配置多个)
    VALID_TOKENS: Set[str] = {
        "expense_api_2025_secure_token",  # 默认token
        "admin_token_2025",               # 管理员token
        # 可以添加更多token
    }
    
    # 从环境变量读取token (优先级更高)
    @classmethod
    def get_valid_tokens(cls) -> Set[str]:
        """获取有效token列表"""
        tokens = cls.VALID_TOKENS.copy()
        
        # 从环境变量添加token
        env_token = os.getenv("EXPENSE_API_TOKEN")
        if env_token:
            tokens.add(env_token)
            
        return tokens
    
    # 是否启用鉴权 (可以通过环境变量关闭，方便开发调试)
    ENABLE_AUTH: bool = os.getenv("EXPENSE_API_AUTH_DISABLED", "false").lower() != "true"


# 使用示例:
# 1. 使用默认token: expense_api_2025_secure_token 或 admin_token_2025
# 2. 设置环境变量: export EXPENSE_API_TOKEN=your_custom_token
# 3. 开发时关闭鉴权: export EXPENSE_API_AUTH_DISABLED=true
# 4. 请求时在Header中添加: Authorization: Bearer your_token
