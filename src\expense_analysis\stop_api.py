#!/usr/bin/env python3
"""
费用分析API服务停止脚本
安全地停止所有相关进程并释放端口
"""
import os
import sys
import psutil
import time
from pathlib import Path

def find_api_processes():
    """查找所有相关的API进程"""
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
            
            # 查找相关进程
            if any(keyword in cmdline.lower() for keyword in [
                'expense_analysis', 'start_api', 'export_api', 'uvicorn'
            ]) and '8002' in cmdline:
                processes.append(proc)
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return processes

def find_port_processes(port=8002):
    """查找占用指定端口的进程"""
    processes = []
    
    try:
        for conn in psutil.net_connections(kind='inet'):
            if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN:
                try:
                    process = psutil.Process(conn.pid)
                    processes.append(process)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
    except Exception as e:
        print(f"⚠️ 检查端口时出错: {e}")
    
    return processes

def stop_process_gracefully(process, timeout=5):
    """优雅地停止进程"""
    try:
        print(f"🔄 正在停止进程: PID {process.pid} ({process.name()})")
        
        # 首先尝试优雅终止
        process.terminate()
        
        # 等待进程退出
        try:
            process.wait(timeout=timeout)
            print(f"✅ 进程 {process.pid} 已优雅退出")
            return True
        except psutil.TimeoutExpired:
            # 如果超时，强制终止
            print(f"⚡ 进程 {process.pid} 超时，强制终止")
            process.kill()
            try:
                process.wait(timeout=2)
                print(f"✅ 进程 {process.pid} 已强制终止")
                return True
            except psutil.TimeoutExpired:
                print(f"❌ 无法终止进程 {process.pid}")
                return False
                
    except psutil.NoSuchProcess:
        print(f"✅ 进程 {process.pid} 已不存在")
        return True
    except psutil.AccessDenied:
        print(f"❌ 无权限终止进程 {process.pid}")
        return False
    except Exception as e:
        print(f"❌ 终止进程 {process.pid} 时出错: {e}")
        return False

def cleanup_pid_file():
    """清理PID文件"""
    pid_file = Path("api_server.pid")
    if pid_file.exists():
        try:
            pid_file.unlink()
            print("✅ PID文件已清理")
        except Exception as e:
            print(f"⚠️ 清理PID文件失败: {e}")

def main():
    """主函数"""
    print("🛑 费用分析API服务停止脚本")
    print("=" * 40)
    
    stopped_count = 0
    
    # 1. 查找API相关进程
    print("🔍 查找API相关进程...")
    api_processes = find_api_processes()
    
    if api_processes:
        print(f"📋 找到 {len(api_processes)} 个相关进程:")
        for proc in api_processes:
            try:
                cmdline = ' '.join(proc.cmdline()[:3])  # 只显示前3个参数
                print(f"  - PID {proc.pid}: {proc.name()} ({cmdline}...)")
            except:
                print(f"  - PID {proc.pid}: {proc.name()}")
        
        print("\n🔄 正在停止API进程...")
        for proc in api_processes:
            if stop_process_gracefully(proc):
                stopped_count += 1
    else:
        print("✅ 未找到API相关进程")
    
    # 2. 检查8002端口占用
    print("\n🔍 检查8002端口占用...")
    port_processes = find_port_processes(8002)
    
    if port_processes:
        print(f"📋 找到 {len(port_processes)} 个占用8002端口的进程:")
        for proc in port_processes:
            try:
                cmdline = ' '.join(proc.cmdline()[:3])
                print(f"  - PID {proc.pid}: {proc.name()} ({cmdline}...)")
            except:
                print(f"  - PID {proc.pid}: {proc.name()}")
        
        print("\n🔄 正在释放8002端口...")
        for proc in port_processes:
            if stop_process_gracefully(proc):
                stopped_count += 1
    else:
        print("✅ 8002端口未被占用")
    
    # 3. 清理PID文件
    print("\n🧹 清理临时文件...")
    cleanup_pid_file()
    
    # 4. 最终检查
    print("\n🔍 最终检查...")
    time.sleep(1)
    
    final_port_processes = find_port_processes(8002)
    if final_port_processes:
        print("⚠️ 仍有进程占用8002端口:")
        for proc in final_port_processes:
            try:
                print(f"  - PID {proc.pid}: {proc.name()}")
            except:
                pass
        print("💡 建议手动检查这些进程")
    else:
        print("✅ 8002端口已完全释放")
    
    print("\n" + "=" * 40)
    if stopped_count > 0:
        print(f"✅ 成功停止 {stopped_count} 个进程")
    else:
        print("ℹ️ 没有需要停止的进程")
    print("🎯 API服务停止完成")

if __name__ == "__main__":
    main()
