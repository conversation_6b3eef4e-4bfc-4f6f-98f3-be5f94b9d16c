"""
性能优化配置
"""

# 并发配置
CONCURRENT_CONFIG = {
    # 线程池配置
    "max_workers": 4,  # 并发线程数，可根据服务器性能调整
    "batch_size": 200,  # 每批次处理的记录数
    
    # AI调用优化
    "ai_timeout": 120,  # AI调用超时时间(秒)
    "retry_attempts": 2,  # 失败重试次数
    
    # 数据库优化
    "db_batch_commit": True,  # 批量提交
    "connection_pool_size": 8,  # 连接池大小
    
    # 性能监控
    "enable_progress_tracking": True,  # 启用进度跟踪
    "log_batch_performance": True,  # 记录批次性能
}

# 根据不同场景的预设配置
PERFORMANCE_PRESETS = {
    "conservative": {
        "max_workers": 2,
        "batch_size": 50,
        "ai_timeout": 180,  # 3分钟超时
        "description": "保守配置，适合资源有限的环境"
    },

    "balanced": {
        "max_workers": 2,  # 减少并发，避免AI服务压力
        "batch_size": 80,  # 减少批量大小
        "ai_timeout": 240,  # 4分钟超时
        "description": "平衡配置，推荐的默认设置"
    },

    "sequential": {
        "max_workers": 1,  # 单线程，避免并发冲突
        "batch_size": 100,
        "ai_timeout": 300,  # 5分钟超时
        "description": "单线程顺序处理，最稳定"
    },

    "aggressive": {
        "max_workers": 3,  # 适度并发
        "batch_size": 60,  # 小批量
        "ai_timeout": 200,  # 较长超时
        "description": "激进配置，适合高性能服务器"
    },

    # 渐进式优化配置
    "step1": {
        "max_workers": 4,
        "batch_size": 100,
        "ai_timeout": 300,
        "description": "第一步优化: 4线程+100条"
    },

    "step2": {
        "max_workers": 6,
        "batch_size": 120,
        "ai_timeout": 360,
        "description": "第二步优化: 6线程+120条"
    },

    "step3": {
        "max_workers": 8,
        "batch_size": 150,
        "ai_timeout": 420,
        "description": "第三步优化: 8线程+150条"
    },

    "ultimate": {
        "max_workers": 10,
        "batch_size": 200,
        "ai_timeout": 480,
        "description": "终极配置: 10线程+200条"
    },

    # 基于8核CPU的优化配置
    "optimal_8core": {
        "max_workers": 4,        # 4个线程
        "batch_size": 10,        # 🔧 大幅减小到10条
        "ai_timeout": 300,
        "adaptive_batch": True,
        "min_batch_size": 5,     # 🔧 最小5条
        "max_batch_size": 15,    # 🔧 最大15条
        "request_delay": 3,      # 每个请求间隔3秒
        "description": "8核CPU优化: 4线程+超小批次(5-15条)+长间隔"
    },

    "max_performance": {
        "max_workers": 16,
        "batch_size": 200,
        "ai_timeout": 360,
        "description": "最大性能: 16线程+200条"
    },

    # 自适应配置
    "adaptive_fast": {
        "max_workers": 6,
        "batch_size": 20,        # 小起始批次
        "ai_timeout": 300,
        "adaptive_batch": True,
        "min_batch_size": 5,
        "max_batch_size": 80,
        "description": "自适应快速: 6线程+动态批次(5-80条)"
    },

    "adaptive_balanced": {
        "max_workers": 8,
        "batch_size": 25,        # 中等起始批次
        "ai_timeout": 300,
        "adaptive_batch": True,
        "min_batch_size": 10,
        "max_batch_size": 120,
        "description": "自适应平衡: 8线程+动态批次(10-120条)"
    },

    "adaptive_aggressive": {
        "max_workers": 10,
        "batch_size": 40,        # 较大起始批次
        "ai_timeout": 360,
        "adaptive_batch": True,
        "min_batch_size": 15,
        "max_batch_size": 200,
        "description": "自适应激进: 10线程+动态批次(15-200条)"
    },

    # 频率控制配置 - 避免429错误
    "rate_limited": {
        "max_workers": 2,        # 低并发
        "batch_size": 20,
        "ai_timeout": 300,
        "request_delay": 1.0,    # 每个请求间隔1秒
        "description": "频率控制: 2线程+1秒间隔 (避免429错误)"
    },

    "rate_limited_fast": {
        "max_workers": 3,        # 中等并发
        "batch_size": 25,
        "ai_timeout": 300,
        "request_delay": 0.5,    # 每个请求间隔0.5秒
        "description": "快速频率控制: 3线程+0.5秒间隔"
    },

    # 🔧 自定义高性能配置
    "custom_high": {
        "max_workers": 8,        # 🔧 8个线程
        "batch_size": 40,        # 🔧 初始批次40条
        "ai_timeout": 300,
        "adaptive_batch": True,
        "min_batch_size": 20,    # 🔧 最小20条
        "max_batch_size": 200,   # 🔧 最大200条
        "request_delay": 0.3,    # 每个请求间隔0.3秒
        "description": "自定义高性能: 8线程+大批次(20-200条)"
    }
}

def get_config(preset="balanced"):
    """获取指定预设的配置"""
    if preset in PERFORMANCE_PRESETS:
        config = CONCURRENT_CONFIG.copy()
        config.update(PERFORMANCE_PRESETS[preset])
        return config
    return CONCURRENT_CONFIG

def print_config_info():
    """打印配置信息"""
    print("🔧 可用的性能配置预设:")
    print("=" * 50)
    
    for preset, config in PERFORMANCE_PRESETS.items():
        print(f"📋 {preset}:")
        print(f"   线程数: {config['max_workers']}")
        print(f"   批量大小: {config['batch_size']}")
        print(f"   说明: {config['description']}")
        print()
