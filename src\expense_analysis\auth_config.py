"""
费用分析模块鉴权配置
"""
import os
import base64
from typing import Set, Dict, Tuple, Optional

# API 鉴权配置
class AuthConfig:
    """鉴权配置"""
    
    # Token与数据库的映射关系 (Token -> 数据库名称)
    # 基于PROVINCE_DATABASE_MAPPING生成的32个省份数据库Token
    TOKEN_DATABASE_MAPPING: Dict[str, str] = {
        # 宁夏 (NX)
        "nx_8f4b5c2937e6d4a1b8f3e2c9d8a7b6e5f4c3b2a1": "analysis_nx",
        # 甘肃 (GS)
        "gs_9a5d6e3f8c2b7a4e1d9f6c3b8e5a2d7f4c1b9e6": "analysis_gs",
        # 河北 (HE)
        "he_7c2f5b8e1a4d9c6f3b8e5a2d7f4c1b9e6a3d8f5": "analysis_he",
        # 贵州 (GZ)
        "gz_6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4": "analysis_gz",
        # 特殊区域 (XXL)
        "xxl_5a0d3c6f9b2e7a4d1c8f5b2e9a6d3c0f7b4e1a9": "xxltidb",
        # 总部 (JT)
        "jt_4f9c2b5e8a1d7c0f3b6e9a2d5c8f1b4e7a0d3c6": "analysis_qg",
        # 北京 (BJ)
        "bj_3e8b1c4f7a0d9c2f5b8e1a4d7c0f3b6e9a2d5c8": "analysis_bj",
        # 天津 (TJ)
        "tj_2d7a0c3f6b9e2a5d8c1f4b7e0a3d6c9f2b5e8a1": "analysis_tj",
        # 山西 (SX)
        "sx_1c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7c0": "analysis_sx",
        # 内蒙古 (NM)
        "nm_0b5e8a1d4c7f0b3e6a9d2c5f8b1e4a7d0c3f6b9": "analysis_nm",
        # 辽宁 (LN)
        "ln_f9a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6f9b2e5a8": "analysis_ln",
        # 吉林 (JL)
        "jl_e8b3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7": "analysis_jl",
        # 黑龙江 (HL)
        "hl_d7c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6": "analysis_hl",
        # 上海 (SH)
        "sh_c6f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5": "analysis_sh",
        # 江苏 (JS)
        "js_b5e0a3d6c9f2b5e8a1d4c7f0b3e6a9d2c5f8b1e4": "analysis_js",
        # 浙江 (ZJ)
        "zj_a4d9c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3": "analysis_zj",
        # 安徽 (AH)
        "ah_98c8f1b4e7a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2": "analysis_ah",
        # 福建 (FJ)
        "fj_87b7e0a3d6c9f2b5e8a1d4c7f0b3e6a9d2c5f8b1": "analysis_fj",
        # 江西 (JX)
        "jx_76a6d9c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0": "analysis_jx",
        # 山东 (SD)
        "sd_65f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6f9": "analysis_sd",
        # 河南 (HA)
        "ha_54e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8": "analysis_ha",
        # 湖北 (HB)
        "hb_43d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4d7": "analysis_hb",
        # 湖南 (HN)
        "hn_32c2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3c6": "analysis_hn",
        # 广东 (GD)
        "gd_21b1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2f5": "analysis_gd",
        # 广西 (GX)
        "gx_10a0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1a4": "analysis_gx",
        # 海南 (HI)
        "hi_0f9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0d3": "analysis_hi",
        # 重庆 (CQ)
        "cq_fe8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9c2": "analysis_cq",
        # 四川 (SC)
        "sc_ed7da0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8e1": "analysis_sc",
        # 云南 (YN)
        "yn_dc6c9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7a0": "analysis_yn",
        # 西藏 (XZ)
        "xz_cb5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6d9": "analysis_xz",
        # 陕西 (SN)
        "sn_ba4a7da0d3c6f9b2e5a8d1c4f7b0e3a6d9c2f5b8": "analysis_sn",
        # 青海 (QH)
        "qh_a939c6c9fc2f5b8e1a4d7c0f3b6e9a2d5c8f1b4e7": "analysis_qh",
        # 新疆 (XJ)
        "xj_9828b5b8eb1e4a7d0c3c6f9b2e5a8d1c4f7b0e3a6": "analysis_xj",
    }

    # 兼容旧版本的简单token列表（从映射中提取）
    @classmethod
    def get_valid_tokens(cls) -> Set[str]:
        """获取所有有效token"""
        tokens = set(cls.TOKEN_DATABASE_MAPPING.keys())

        # 从环境变量添加token（格式：token:database）
        env_token_db = os.getenv("EXPENSE_API_TOKEN_DB")
        if env_token_db and ":" in env_token_db:
            token, database = env_token_db.split(":", 1)
            cls.TOKEN_DATABASE_MAPPING[token] = database
            tokens.add(token)

        return tokens
    
    @classmethod
    def get_database_by_token(cls, token: str) -> Optional[str]:
        """根据token获取对应的数据库名称"""
        # 先检查映射表
        database = cls.TOKEN_DATABASE_MAPPING.get(token)
        if database:
            return database

        # 检查环境变量
        env_token_db = os.getenv("EXPENSE_API_TOKEN_DB")
        if env_token_db and ":" in env_token_db:
            env_token, env_database = env_token_db.split(":", 1)
            if env_token == token:
                return env_database

        return None

    @classmethod
    def is_valid_token(cls, token: str) -> bool:
        """检查token是否有效"""
        return token in cls.get_valid_tokens()
    
    # 是否启用鉴权 (可以通过环境变量关闭，方便开发调试)
    ENABLE_AUTH: bool = os.getenv("EXPENSE_API_AUTH_DISABLED", "false").lower() != "true"


# 使用示例:
# 1. 使用预设token访问对应省份数据库:
#    - Authorization: Bearer gz_6b1e4a7d0c3f8b5e2a7d4c1f9b6e3a8d5c2f7b4  (访问贵州数据库)
#    - Authorization: Bearer bj_3e8b1c4f7a0d9c2f5b8e1a4d7c0f3b6e9a2d5c8  (访问北京数据库)
#    - Authorization: Bearer jt_4f9c2b5e8a1d7c0f3b6e9a2d5c8f1b4e7a0d3c6  (访问总部数据库)
# 2. 设置环境变量: export EXPENSE_API_TOKEN_DB=your_token:your_database
# 3. 开发时关闭鉴权: export EXPENSE_API_AUTH_DISABLED=true
# 4. Token自动决定访问哪个数据库，用户无法直接修改
# 5. 支持全国32个省份+总部+特殊区域，共34个数据库
