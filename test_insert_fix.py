#!/usr/bin/env python3
"""
测试修复后的插入功能
"""

from src.expense_analysis.services.data_service import DataService

def test_insert_with_rpt_month():
    print("=== 测试修复后的插入功能 ===")
    
    try:
        service = DataService(database_name='analysis_gz')
        
        # 测试插入一条记录，包含rpt_month
        test_id = 'test_rpt_month_123'
        test_category = '测试分类'
        test_rpt_month = '202303'
        
        print(f"测试插入: ID={test_id}, 分类={test_category}, 月份={test_rpt_month}")
        
        # 先删除可能存在的测试记录
        connection = service.get_connection()
        cursor = connection.cursor()
        cursor.execute('DELETE FROM ele_payment_ai_sort WHERE billaccountpaymentdetail_id = %s', (test_id,))
        connection.commit()
        cursor.close()
        connection.close()
        
        # 测试插入
        result = service.save_classification_result(test_id, test_category, test_rpt_month)
        print(f"插入结果: {result}")
        
        # 验证插入
        connection = service.get_connection()
        cursor = connection.cursor()
        cursor.execute('''
            SELECT billaccountpaymentdetail_id, ai_category, rpt_month, ai_classified_time 
            FROM ele_payment_ai_sort 
            WHERE billaccountpaymentdetail_id = %s
        ''', (test_id,))
        
        test_record = cursor.fetchone()
        if test_record:
            print(f"✅ 测试记录插入成功:")
            print(f"   ID: {test_record[0]}")
            print(f"   分类: {test_record[1]}")
            print(f"   月份: {test_record[2]}")
            print(f"   时间: {test_record[3]}")
            
            # 验证可以通过rpt_month查询
            cursor.execute('''
                SELECT COUNT(*) FROM ele_payment_ai_sort 
                WHERE rpt_month = %s AND billaccountpaymentdetail_id = %s
            ''', (test_rpt_month, test_id))
            
            count = cursor.fetchone()[0]
            if count > 0:
                print(f"✅ 可以通过rpt_month={test_rpt_month}查询到记录")
            else:
                print(f"❌ 无法通过rpt_month={test_rpt_month}查询到记录")
        else:
            print("❌ 测试记录插入失败")
        
        # 清理测试记录
        cursor.execute('DELETE FROM ele_payment_ai_sort WHERE billaccountpaymentdetail_id = %s', (test_id,))
        connection.commit()
        print("🧹 测试记录已清理")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_insert_with_rpt_month()
