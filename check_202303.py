#!/usr/bin/env python3
"""
检查202303的分类数据
"""

from src.expense_analysis.services.data_service import DataService

def check_202303_data():
    print("=== 检查202303的分类数据 ===")
    
    try:
        service = DataService(database_name='analysis_gz')
        connection = service.get_connection()
        cursor = connection.cursor()
        
        # 1. 检查clean_ele_payment表中202303的原始数据
        print("1. 检查202303的原始数据...")
        cursor.execute('''
            SELECT COUNT(*) 
            FROM clean_ele_payment 
            WHERE billamount_date >= %s AND billamount_date <= %s
        ''', ('2023-03-01', '2023-03-31'))
        
        original_count = cursor.fetchone()[0]
        print(f"   202303原始记录数: {original_count}")
        
        # 2. 检查202303已分类的数据
        print("\n2. 检查202303已分类的数据...")
        cursor.execute('''
            SELECT COUNT(*) 
            FROM clean_ele_payment c 
            JOIN ele_payment_ai_sort s ON c.billaccountpaymentdetail_id = s.billaccountpaymentdetail_id 
            WHERE c.billamount_date >= %s AND c.billamount_date <= %s
        ''', ('2023-03-01', '2023-03-31'))
        
        classified_count = cursor.fetchone()[0]
        print(f"   202303已分类记录数: {classified_count}")
        
        # 3. 如果有分类数据，显示分类统计
        if classified_count > 0:
            print("\n3. 202303分类统计:")
            cursor.execute('''
                SELECT s.ai_category, COUNT(*) as count
                FROM clean_ele_payment c 
                JOIN ele_payment_ai_sort s ON c.billaccountpaymentdetail_id = s.billaccountpaymentdetail_id 
                WHERE c.billamount_date >= %s AND c.billamount_date <= %s
                GROUP BY s.ai_category
                ORDER BY count DESC
            ''', ('2023-03-01', '2023-03-31'))
            
            results = cursor.fetchall()
            for row in results:
                print(f"   {row[0]}: {row[1]}条")
        else:
            print("\n❌ 202303没有分类数据!")
            
            # 检查是否有其他月份的分类数据
            print("\n4. 检查其他月份的分类数据...")
            cursor.execute('''
                SELECT 
                    DATE_FORMAT(c.billamount_date, '%Y-%m') as month,
                    COUNT(*) as count
                FROM clean_ele_payment c 
                JOIN ele_payment_ai_sort s ON c.billaccountpaymentdetail_id = s.billaccountpaymentdetail_id 
                GROUP BY DATE_FORMAT(c.billamount_date, '%Y-%m')
                ORDER BY month DESC
                LIMIT 10
            ''')
            
            month_results = cursor.fetchall()
            if month_results:
                print("   已分类的月份:")
                for row in month_results:
                    print(f"   {row[0]}: {row[1]}条")
            else:
                print("   ❌ 没有任何月份的分类数据!")
        
        # 5. 检查最近的分类记录属于哪个月份
        print("\n5. 最近分类记录的月份:")
        cursor.execute('''
            SELECT 
                c.billamount_date,
                s.ai_category,
                s.ai_classified_time
            FROM clean_ele_payment c 
            JOIN ele_payment_ai_sort s ON c.billaccountpaymentdetail_id = s.billaccountpaymentdetail_id 
            ORDER BY s.ai_classified_time DESC
            LIMIT 5
        ''')
        
        recent_results = cursor.fetchall()
        for i, row in enumerate(recent_results, 1):
            print(f"   {i}. 账单日期: {row[0]}, 分类: {row[1]}, 分类时间: {row[2]}")
        
        cursor.close()
        connection.close()
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_202303_data()
