#!/usr/bin/env python3
"""
费用分类服务
统一的分类API服务，支持Token鉴权和多数据库切换
"""
import os
import sys
import argparse
import time
import threading
import queue
import concurrent.futures
from datetime import datetime
from typing import List, Dict, Optional, Any
from fastapi import FastAPI, HTTPException, Query, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
import uvicorn
import logging

# 添加项目路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.expense_analysis.services.data_service import DataService
from src.expense_analysis.agents import ExpenseClassificationAgent
from src.expense_analysis.config.performance_config import get_config
from src.expense_analysis.models.expense_models import ExpenseRecord
from src.expense_analysis.auth import verify_token_with_database
from src.expense_analysis.auth_config import AuthConfig

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="费用分类服务API",
    description="AI费用分类服务，支持发现分类和执行分类",
    version="1.0.0"
)

# 全局变量
classification_tasks = {}  # 存储分类任务状态
task_lock = threading.Lock()

class ClassificationService:
    """分类服务类"""
    
    def __init__(self, database_name: str = "analysis_gz"):
        self.database_name = database_name
        self.data_service = DataService(database_name=database_name)
        self.agent = ExpenseClassificationAgent()
        self.config = get_config()
    
    def discover_categories(self, year_month: str = None, limit: int = 1000) -> Dict[str, Any]:
        """发现分类类别"""
        logger.info(f"开始发现分类类别，数据库: {self.database_name}, 年月: {year_month}")

        try:
            # 构建过滤条件
            filters = {}
            if year_month:
                filters['rpt_month'] = year_month

            # 使用原有的采样逻辑
            table_name = "clean_ele_payment"
            remarks = self.data_service.get_stratified_sample(table_name, filters, limit=limit)
            logger.info(f"获取到 {len(remarks)} 条备注样本")

            if not remarks:
                return {
                    "status": "success",
                    "message": "没有找到有效的备注数据",
                    "categories": [],
                    "total_records": 0,
                    "filters": filters
                }

            # 使用AI发现分类
            categories = self.agent.discover_categories(remarks)

            result = {
                "status": "success",
                "message": f"成功发现 {len(categories)} 个分类类别",
                "categories": categories,
                "total_records": len(remarks),
                "database": self.database_name,
                "filters": filters,
                "timestamp": datetime.now().isoformat()
            }

            logger.info(f"分类发现完成: {len(categories)} 个类别")
            return result

        except Exception as e:
            logger.error(f"分类发现失败: {e}")
            raise HTTPException(status_code=500, detail=f"分类发现失败: {str(e)}")
    
    def classify_records(self, year_month: str = None, clear_data: bool = False,
                        limit: int = None, batch_size: int = 50) -> str:
        """执行分类任务（异步）"""
        task_id = f"classify_{int(time.time())}"

        with task_lock:
            classification_tasks[task_id] = {
                "status": "running",
                "progress": 0,
                "total": 0,
                "processed": 0,
                "start_time": datetime.now().isoformat(),
                "database": self.database_name,
                "year_month": year_month,
                "clear_data": clear_data,
                "filters": {"rpt_month": year_month} if year_month else {}
            }

        # 启动后台分类任务
        def run_classification():
            try:
                self._execute_classification(task_id, year_month, clear_data, limit, batch_size)
            except Exception as e:
                logger.error(f"分类任务失败: {e}")
                with task_lock:
                    classification_tasks[task_id]["status"] = "failed"
                    classification_tasks[task_id]["error"] = str(e)

        thread = threading.Thread(target=run_classification)
        thread.daemon = True
        thread.start()

        return task_id
    
    def _execute_classification(self, task_id: str, year_month: str = None,
                               clear_data: bool = False, limit: int = None, batch_size: int = 50):
        """执行分类的内部方法"""
        try:
            # 构建过滤条件
            filters = {}
            if year_month:
                filters['rpt_month'] = year_month

            # 如果需要清理数据，先清理指定条件的分类结果
            if clear_data:
                logger.info(f"清理数据: {filters}")
                self.data_service.clear_classification_results(filters)

            # 获取需要分类的记录
            table_name = "clean_ele_payment"

            # 获取所有符合条件的记录（而不是样本）
            records = self.data_service.get_records_for_classification(table_name, filters, limit)
            total = len(records)

            with task_lock:
                classification_tasks[task_id]["total"] = total

            if total == 0:
                with task_lock:
                    classification_tasks[task_id]["status"] = "completed"
                    classification_tasks[task_id]["message"] = "没有需要分类的记录"
                return

            logger.info(f"开始分类 {total} 条记录，年月: {year_month}")

            # 批量处理
            processed = 0
            for i in range(0, total, batch_size):
                batch = records[i:i + batch_size]

                # 分类批次
                for record in batch:
                    try:
                        # 获取备注内容（支持两个字段）
                        remark_content = None
                        if hasattr(record, 'paymentdetail_note') and record.paymentdetail_note:
                            remark_content = record.paymentdetail_note
                        elif hasattr(record, 'mandatory_note') and record.mandatory_note:
                            remark_content = record.mandatory_note

                        if remark_content:  # 只分类有备注的记录
                            category = self.agent.classify_single(remark_content)
                            self.data_service.save_classification_result(record.billaccountpaymentdetail_id, category)
                            processed += 1

                        # 更新进度
                        with task_lock:
                            classification_tasks[task_id]["processed"] = processed
                            classification_tasks[task_id]["progress"] = round(processed / total * 100, 2)

                    except Exception as e:
                        logger.error(f"分类记录 {record.id} 失败: {e}")

                # 批次间延迟
                time.sleep(0.1)

            # 完成
            with task_lock:
                classification_tasks[task_id]["status"] = "completed"
                classification_tasks[task_id]["end_time"] = datetime.now().isoformat()
                classification_tasks[task_id]["message"] = f"成功分类 {processed} 条记录"

            logger.info(f"分类任务完成: {processed}/{total}")

        except Exception as e:
            logger.error(f"分类执行失败: {e}")
            with task_lock:
                classification_tasks[task_id]["status"] = "failed"
                classification_tasks[task_id]["error"] = str(e)

# API接口
@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "ok", "service": "classification", "timestamp": datetime.now().isoformat()}

@app.post("/classify/discover")
async def discover_categories(
    year_month: Optional[str] = Query(None, description="年月(YYYYMM)，如202301"),
    limit: int = Query(1000, description="样本数量限制"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """发现分类类别"""
    _, database = auth_info
    service = ClassificationService(database_name=database)
    return service.discover_categories(year_month=year_month, limit=limit)

@app.post("/classify/start")
async def start_classification(
    year_month: Optional[str] = Query(None, description="年月(YYYYMM)，如202301"),
    clear_data: bool = Query(False, description="是否清理已有分类数据"),
    limit: Optional[int] = Query(None, description="分类记录数量限制"),
    batch_size: int = Query(50, description="批次大小"),
    auth_info: tuple = Depends(verify_token_with_database)
):
    """开始分类任务"""
    _, database = auth_info
    service = ClassificationService(database_name=database)
    task_id = service.classify_records(
        year_month=year_month,
        clear_data=clear_data,
        limit=limit,
        batch_size=batch_size
    )

    return {
        "status": "success",
        "task_id": task_id,
        "message": "分类任务已启动",
        "database": database,
        "year_month": year_month,
        "clear_data": clear_data
    }

@app.get("/classify/status/{task_id}")
async def get_task_status(
    task_id: str,
    auth_info: tuple = Depends(verify_token_with_database)
):
    """获取分类任务状态"""
    if task_id not in classification_tasks:
        raise HTTPException(status_code=404, detail="任务不存在")

    return classification_tasks[task_id]

@app.get("/classify/tasks")
async def list_tasks(
    auth_info: tuple = Depends(verify_token_with_database)
):
    """列出所有分类任务"""
    _, database = auth_info

    # 过滤当前数据库的任务
    filtered_tasks = {
        task_id: task_info
        for task_id, task_info in classification_tasks.items()
        if task_info.get("database") == database
    }

    return {
        "tasks": filtered_tasks,
        "database": database,
        "total": len(filtered_tasks)
    }

@app.get("/classify/statistics")
async def get_statistics(
    auth_info: tuple = Depends(verify_token_with_database)
):
    """获取分类统计信息"""
    _, database = auth_info
    service = ClassificationService(database_name=database)
    
    try:
        # 获取统计信息
        stats = service.data_service.get_classification_statistics()
        
        return {
            "status": "success",
            "database": database,
            "statistics": stats,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

def start_server(port: int = 8003):
    """启动分类服务"""
    print("🤖 启动费用分类服务")
    print("=" * 40)
    print(f"📍 端口: {port}")
    print(f"📖 文档: http://localhost:{port}/docs")
    print("🛑 停止: Ctrl+C")
    print("=" * 40)
    
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 分类服务已停止")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="费用分类服务")
    parser.add_argument("action", nargs="?", choices=["start", "discover", "classify"], 
                       default="start", help="操作类型")
    parser.add_argument("--port", type=int, default=8003, help="服务端口")
    parser.add_argument("--database", default="analysis_gz", help="数据库名称")
    parser.add_argument("--limit", type=int, help="记录数量限制")
    
    args = parser.parse_args()
    
    if args.action == "start":
        start_server(args.port)
    elif args.action == "discover":
        # 命令行模式发现分类
        service = ClassificationService(args.database)
        result = service.discover_categories(limit=args.limit or 1000)
        print(f"发现结果: {result}")
    elif args.action == "classify":
        # 命令行模式执行分类
        service = ClassificationService(args.database)
        task_id = service.classify_records(limit=args.limit)
        print(f"分类任务已启动: {task_id}")

if __name__ == "__main__":
    main()
